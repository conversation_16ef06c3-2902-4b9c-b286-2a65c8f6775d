<header class="w-full py-5 md:py-6 flex {% unless template.name == 'index' %}border-b border-gray-2{% endunless %}">
  <div class="w-full relative flex justify-between items-start px-4 lg:container lg:px-0">
    <div class="header-logo-block">
      {% render 'logo-block' %}
    </div>
    <div class="flex justify-end gap-2 lg:hidden">
      <button
        data-drawer-target="mobile-popup-cart-item"
        data-drawer-show="mobile-popup-cart-item"
        data-drawer-placement="laft"
        aria-controls="mobile-popup-cart-item"
        type="button"
        class="flex gap-1 h-9 py-[7px] px-3 text-sm font-semibold outline-secondary text-secondary"
      >
        {{ 'cart.nav_link_text' | t }}
        <span
          class="cart-count-badge inline-flex justify-center items-center w-[22px] h-[22px] rounded-full text-sm font-normal leading-[0px] bg-secondary text-white"
        >
          {{- cart.item_count -}}
        </span>
      </button>
      <button id="openMobileNavigation" class="block lg:hidden">
        {{- 'icon-hamburger-menu.svg' | inline_asset_content -}}
      </button>
    </div>
    <nav
      role="navigation"
      id="navbarCollapse"
      class="absolute bg-white z-[999] transition duration-300 right-0 top-0 lg:h-auto w-full px-4 lg:px-0 lg:my-auto lg:static lg:block lg:w-full lg:max-w-full lg:bg-transparent hidden"
    >
      <div class="flex justify-end mb-4 lg:hidden">
        <button id="closeMobileNavigation">
          {{- 'icon-cross.svg' | inline_asset_content -}}
        </button>
      </div>
      <div
        class="menu-drawer-navigation-container flex flex-col justify-between lg:flex-row lg:content-normal lg:justify-end lg:items-center gap-4"
      >
        <div class="link-block flex flex-col lg:flex-row  lg:justify-end lg:items-center gap-6">
          <ul class="flex flex-col md:flex-row gap-4 md:gap-6">
            {%- for link in linklists['main-menu'].links -%}
              <li class="flex flex-col md:flex-row relative {% if link.links != blank -%}submenu-item{% endif -%}">
                <a
                  {% if link.links != blank %}
                    role="button"
                  {% else %}
                    href="{{- link.url -}}"
                  {% endif %}
                  {% if link.active %}
                    aria-current="page"
                  {% endif %}
                  class="flex items-center justify-between text-base lg:mr-0 lg:inline-flex lg:px-0 {% if link.active %}active-link{% endif %}"
                >
                  {{- link.title -}}
                  {%- if link.links != blank -%}
                    <span class="ml-2 size-5 caret-icon">
                      <svg
                        width="18"
                        height="18"
                        class="transform transition-transform duration-300 ease"
                        viewBox="0 0 25 24"
                        fill="currentColor"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g id="Chevron-Down"><path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M5.96967 8.46967C6.26256 8.17678 6.73744 8.17678 7.03033 8.46967L12.5 13.9393L17.9697 8.46967C18.2626 8.17678 18.7374 8.17678 19.0303 8.46967C19.3232 8.76256 19.3232 9.23744 19.0303 9.53033L13.0303 15.5303C12.7374 15.8232 12.2626 15.8232 11.9697 15.5303L5.96967 9.53033C5.67678 9.23744 5.67678 8.76256 5.96967 8.46967Z" fill="currentColor"/></g>
                      </svg>
                    </span>
                  {%- endif -%}
                </a>
                {%- if link.links != blank -%}
                  <div class="submenu hidden relative md:top-full left-0 md:invisible md:absolute md:block md:opacity-0 w-full md:w-60">
                    <div class="divide-y divide-gray-2 mt-4 bg-white border border-gray-2 rounded-lg shadow-sm md:shadow-lg">
                      {%- for child_link in link.links -%}
                        <a
                          href="{{- child_link.url -}}"
                          {% if child_link.active %}
                            aria-current="page"
                          {% endif %}
                          class="flex items-center justify-between w-full py-3 px-5 text-base hover:text-primary {% if child_link.active %}active-link{% endif %}"
                        >
                          {{- child_link.title -}}
                        </a>
                      {%- endfor -%}
                    </div>
                  </div>
                {%- endif -%}
              </li>
            {%- endfor -%}
          </ul>
          {%- if customer -%}
            <div class="user-profile  block lg:hidden">
              <hr class="my-4 border-gray-2 sm:mx-auto">
              <div class="grid divide-y divide-neutral-200 max-w-xl mx-auto mt-6">
                <details class="group">
                  <summary class="flex gap-2 items-center justify-between font-medium cursor-pointer mb-4">
                    <span class="text-[22px] leading-7 pointer-events-none font-bold text-primary-gradient">
                      {{ 'general.customer.profile' | t -}}
                    </span>
                    <span class="transition group-open:rotate-180">
                      <img src="{{ 'chevron-down-primary.svg' | asset_url }}" width="25" height="24" loading="eager">
                    </span>
                  </summary>
                  <ul class="header-profile-link-block flex flex-col gap-4 mt-4 md:mt-0">
                    {%- for link in linklists['account-menu'].links -%}
                      <li class="block w-full text-base">
                        <a href="{{ link.url }}" data-block-id="{{- link.handle -}}" class="account-links">
                          {{- link.title -}}
                        </a>
                      </li>
                    {% endfor %}
                  </ul>
                </details>
              </div>
            </div>
          {% endif %}
        </div>
        <div class="button-block flex flex-col lg:flex-row justify-center items-center gap-4">
          <div class="hidden lg:block">
            <a
              href="{{- routes.cart_url -}}"
              data-popover-target="cart-popup"
              data-popover-trigger="hover"
              type="button"
              class="flex gap-1 h-9 py-[7px] px-3 text-sm font-semibold leading-[22px] outline-secondary text-secondary bg-white"
            >
              {{- 'cart.nav_link_text' | t -}}
              <span
                class="cart-count-badge inline-flex justify-center items-center w-[22px] h-[22px] rounded-full text-sm font-normal leading-[0px] bg-secondary text-white"
              >
                {{- cart.item_count -}}
              </span>
            </a>
          </div>
          {%- if shop.customer_accounts_enabled -%}
            {% if customer %}
              <button
                data-dropdown-toggle="profileDropdown"
                aria-expanded="false"
                data-dropdown-offset-skidding="-100"
                class="rounded-full text-base leading-[0] bg-gray-2 px-[9px] py-[18px] text-gray-8 text-center hidden lg:block"
              >
                {{- customer.first_name | first | upcase -}}
                {{- customer.last_name | first | upcase -}}
              </button>
              <div
                id="profileDropdown"
                class="z-10 hidden transition-opacity duration-300 font-normal bg-white border border-gray-2 rounded-lg shadow-lg w-60"
              >
                <ul class=" text-sm text-gray-8 divide-y divide-gray-2 ">
                  <li class="p-4">
                    <a
                      href="{{- routes.account_url -}}"
                      class="profile-dropdown-item gap-2 items-center flex w-full text-base"
                    >
                      <span>{{- 'icon-profile.svg' | inline_asset_content -}}</span>
                      {{ 'general.customer.profile' | t }}
                    </a>
                  </li>
                  {%- if settings.version != blank -%}
                    <li class="p-4">
                      <div class="profile-dropdown-item gap-2 items-center flex w-full text-base">
                        <span>{{- 'icon-version.svg' | inline_asset_content -}}</span>
                        {{ 'general.version' | t }}: {{ settings.version }}
                      </div>
                    </li>
                  {% endif %}
                  <li class="p-4">
                    <a
                      href="{{ routes.account_logout_url }}"
                      class="profile-dropdown-item gap-2 items-center flex w-full text-base"
                    >
                      <span>{{- 'icon-logout.svg' | inline_asset_content -}}</span>
                      {{ 'general.customer.logout' | t }}
                    </a>
                  </li>
                </ul>
              </div>
            {% endif %}
            <a
              href="{%- if customer -%}{{ routes.account_logout_url }}{%  else %}{{  routes.account_login_url }}{% endif %}"
              class="{%- if customer -%}button-primary-light block lg:hidden{%  else %}button-primary-gradient{% endif %} w-full text-center !py-2.5 text-sm font-semibold"
            >
              {%- if customer -%}
                <span class="text-primary-gradient">{{ 'general.customer.logout' | t }}</span>
              {% else -%}
                {{- 'general.customer.login' | t }}
              {% endif %}
            </a>
          {%- endif -%}
        </div>
      </div>
    </nav>
  </div>
</header>
{% assign message = 'cart.item_added_in_cart' | t %}
{% render 'primary-notification', message: message, id: 'cart-notification' %}
