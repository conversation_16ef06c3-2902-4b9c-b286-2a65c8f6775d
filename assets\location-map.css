.gm-style .gm-style-iw-c {
  flex-direction: row-reverse !important;
  padding-left: 18px !important;
}

.gm-style-iw-d {
  overflow: hidden !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.marker-icon,
.custom-cluster {
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  -ms-user-drag: none;
}

.marker-icon {
  transition: transform 0.3s ease, filter 0.3s ease;
  transform-origin: bottom center;
}

.selected-marker .marker-icon {
  transform: scale(1.35);
  filter: drop-shadow(0 0 6px #e9e9e999);
}

/* Loading state styles for filter elements */
.filter-wrapper[data-loading='true'] {
  position: relative;
}

.filter-wrapper[data-loading='true']::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
}

.filter-wrapper[data-loading='true'] .near-me-button,
.filter-wrapper[data-loading='true'] .search-input,
.filter-wrapper[data-loading='true'] .filter-dropdown button {
  cursor: not-allowed;
  pointer-events: none;
}

@keyframes rotateAnimation {
  0% {
    transform: rotateY(0deg);
  }

  100% {
    transform: rotateY(45deg);
  }
}

.custom-bg-overlay-top,
.custom-bg-overlay-bottom {
  position: absolute;
  width: 100%;
  height: 172px;
}

.custom-bg-overlay-top {
  top: 0;
  background: linear-gradient(180deg, var(--color-white) 0%, rgba(255, 255, 255, 0) 100%);
}

.custom-bg-overlay-bottom {
  bottom: 0;
  background: linear-gradient(180deg, var(--color-white) 0%, rgba(255, 255, 255, 0) 100%);
}

input::-webkit-search-cancel-button {
  position: relative;
  -webkit-appearance: none;
  height: 18px;
  width: 18px;
  background: url('data:image/svg+xml,%3Csvg%20width%3D%2218%22%20height%3D%2218%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20id%3D%22Cross%22%3E%3Cpath%20id%3D%22Icon%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M5.46967%205.46967C5.76256%205.17678%206.23744%205.17678%206.53033%205.46967L12%2010.9393L17.4697%205.46967C17.7626%205.17678%2018.2374%205.17678%2018.5303%205.46967C18.8232%205.76256%2018.8232%206.23744%2018.5303%206.53033L13.0607%2012L18.5303%2017.4697C18.8232%2017.7626%2018.8232%2018.2374%2018.5303%2018.5303C18.2374%2018.8232%2017.7626%2018.8232%2017.4697%2018.5303L12%2013.0607L6.53033%2018.5303C6.23744%2018.8232%205.76256%2018.8232%205.46967%2018.5303C5.17678%2018.2374%205.17678%2017.7626%205.46967%2017.4697L10.9393%2012L5.46967%206.53033C5.17678%206.23744%205.17678%205.76256%205.46967%205.46967Z%22%20fill%3D%22%2322282F%22%20%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E')
    no-repeat;
  cursor: pointer;
}

input::-webkit-search-cancel-button:hover {
  background: url('data:image/svg+xml,%3Csvg%20width%3D%2218%22%20height%3D%2218%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cg%20id%3D%22Cross%22%3E%3Cpath%20id%3D%22Icon%22%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M5.46967%205.46967C5.76256%205.17678%206.23744%205.17678%206.53033%205.46967L12%2010.9393L17.4697%205.46967C17.7626%205.17678%2018.2374%205.17678%2018.5303%205.46967C18.8232%205.76256%2018.8232%206.23744%2018.5303%206.53033L13.0607%2012L18.5303%2017.4697C18.8232%2017.7626%2018.8232%2018.2374%2018.5303%2018.5303C18.2374%2018.8232%2017.7626%2018.8232%2017.4697%2018.5303L12%2013.0607L6.53033%2018.5303C6.23744%2018.8232%205.76256%2018.8232%205.46967%2018.5303C5.17678%2018.2374%205.17678%2017.7626%205.46967%2017.4697L10.9393%2012L5.46967%206.53033C5.17678%206.23744%205.17678%205.76256%205.46967%205.46967Z%22%20fill%3D%22%23D22725%22%20%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E')
    no-repeat;
}
/* loader.css */
.loader-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(32, 32, 32, 0.8);
  z-index: 8;
  opacity: 1;
  visibility: visible;
  transition: opacity 0.5s ease, visibility 0.5s ease;
  border-radius: 8px;
}

#businessTypeSelectorToggle:focus,
#radiusSelectorToggle:focus {
  outline: none;
  border-radius: 6px;
  border: 1px solid var(--color-primary) !important;
  background: var(--color-white);
  box-shadow: 0px 0px 0px 4px var(--color-blush-pink);
}

.loader-wrapper.hidden {
  opacity: 0;
  visibility: hidden;
}

.loader {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  position: relative;
  animation: rotate 1.25s linear infinite;
}

.loader::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  inset: 0px;
  border-radius: 50%;
  border: 4px solid var(--color-primary);
  animation: spinner 2s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spinner {
  0% {
    clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0);
  }
  25% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0);
  }
  50% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%);
  }
  75% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%);
  }
  100% {
    clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 0 100%, 0 0);
  }
}

map-location .near-me-button.selected {
  border-radius: 6px;
  border: 1px solid var(--color-primary);
  background: var(--color-primary-light);
}

.filter-options {
  z-index: 8;
}

map-location input:focus {
  border-radius: 6px;
  border: 1px solid var(--color-primary) !important;
  background: var(--color-white);
  box-shadow: 0px 0px 0px 4px var(--color-blush-pink);
}

/* Custom marker cluster styles */
.custom-cluster {
  background: var(--gradient-primary);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  outline: 5px solid rgba(22, 22, 24, 0.7);
  cursor: pointer;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.custom-cluster:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.cluster-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.cluster-count {
  color: white;
  font-size: 12px;
  font-weight: 700;
}

@media (max-width: 768px) {
  .sidebar-location {
    order: 1;
    overflow-y: scroll;
    min-height: 250px;
    height: auto;
    max-height: 480px;
    padding-right: 8px;
  }

  .gm-style .gm-style-iw-c {
    flex-direction: row-reverse !important;
    padding-left: 10px !important;
    border-radius: 4px !important;
    padding: 0 0 0 16px !important;
  }

  #map {
    height: 298px !important;
    width: 100%;
  }
  map-location {
    position: relative;
  }
}
