image: atlassian/default-image:4

pipelines:
  branches:
    dev:
      - step:
          name: "Deployment to the development environment"
          deployment: development
          script:
            - apt-get update && apt-get install -y ruby-full
            - apt-get install ruby-dev
            - gem install bundler
            - npm install -g @shopify/cli @shopify/theme
            - shopify version
            - shopify theme list --store=$SHOPIFY_FLAG_STORE --password=$SHOPIFY_CLI_THEME_TOKEN
            - shopify theme push --store=$SHOPIFY_FLAG_STORE --password=$SHOPIFY_CLI_THEME_TOKEN --path $BITBUCKET_CLONE_DIR/ --theme=$SHOPIFY_THEME_ID --force  --verbose
    master:
      - step:
          name: "Deployment to the production environment"
          deployment: production
          script:
            - apt-get update && apt-get install -y ruby-full
            - apt-get install ruby-dev
            - gem install bundler
            - npm install -g @shopify/cli @shopify/theme
            - shopify version
            - shopify theme list --store=$SHOPIFY_FLAG_STORE --password=$SHOPIFY_CLI_THEME_TOKEN
            - shopify theme push --store=$SHOPIFY_FLAG_STORE --password=$SHOPIFY_CLI_THEME_TOKEN --path $BITBUCKET_CLONE_DIR/ --theme=$SHOPIFY_THEME_ID  --allow-live --force  --verbose
