{%- style -%}
  .image-wrapper img {
    width: 180px;
    height: 445 px;
    flex-shrink: 0;
  }

  .image-wrapper.second-image img {
    width: 138px;
    height: 328px;
    flex-shrink: 0;
  }

  .app-button-block a img {
    height: 55px;
    width: auto;
  }

  .shape-icon-top-right {
    z-index: 0;
    position: absolute;
    width: 533.073px;
    height: 252.961px;
    right: -60px;
    top: 140px;
  }

  .shape-icon-left-bottom {
    left: 0;
    bottom: 38px;
    z-index: 0;
    position: absolute;
    width: 524px;
    height: 236px;
    z-index: -1;
    border-radius: 0 220px 220px 0;
    opacity: 0.4;
    background: #c2d3d2;
  }
  @media screen and (max-width: 768px) {
    .shape-icon-top-right {
      right: -62px;
      top: auto;
      bottom: 132px;
      width: 406px;
      height: auto;
    }

    .shape-icon-left-bottom {
      top: 360px;
      bottom: auto;
      width: 350px;
      height: 150px;
    }

    .image-wrapper img {
      width: 40%;
      height: auto;
    }
    .image-wrapper.first-image {
      margin-bottom: 48px;
    }

    .image-wrapper.second-image img {
      width: 30%;
      height: auto;
    }
  }
{%- endstyle -%}
<div class="digital-mobile-experience-wrapper section-{{ section.id }}-margin overflow-hidden py-12 md:py-16 lg:py-20 relative">
  <div class="shape-icon-top-right">
    <svg viewBox="0 0 534 253" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path opacity="0.4" d="M0 251.695C148.51 252.65 463.039 253.988 533.073 251.695C533.073 -79.7168 2.9701e-05 -88.0542 0 251.695Z" fill="#F84642"/>
    </svg>
  </div>
  <div class="container px-4 lg:px-0">
    <div class="image-block gap-12 flex flex-col justify-evenly md:flex-row py-8">
      <div class="image-wrapper first-image flex flex-col justify-center items-center text-center">
        {% if section.settings.image_first -%}
          {%- liquid
            assign height = section.settings.image_first.width | divided_by: section.settings.image_first.aspect_ratio | round
            assign sizes = '100vw'
            assign widths = '375, 550, 750'
            assign fetch_priority = 'high'
            assign classes = 'image-block lazy-load blur-loading'
          -%}
          {{
            section.settings.image_first
            | image_url: width: 1200
            | image_tag:
              loading: 'lazy',
              height: height,
              sizes: sizes,
              widths: widths,
              class: classes,
              fetchpriority: fetch_priority
          }}
        {% endif %}
      </div>
      <div class="content-inner-block flex flex-col justify-center items-center text-center">
        <div class="text-block flex flex-col flex-shrink-0 w-full md:w-96">
          <p class="text-xl font-bold md:text-[28px] md:leading-9 text-secondary">
            {{- section.settings.content_first -}}
          </p>
          <h2 class="text-[28px] font-bold leading-tight md:text-[52px] text-[#4C6A68]">
            {{- section.settings.content_second -}}
          </h2>
          <p class="text-xl font-bold md:text-[28px] md:leading-9 text-secondary">
            {{- section.settings.content_third -}}
          </p>
        </div>

        <div class="app-button-block flex flex-wrap gap-4 mt-8">
          {%- for block in section.blocks -%}
            {% case block.type %}
              {% when 'button' %}
                {%- if block.settings.button_icon -%}
                  <a
                    href="{{- block.settings.button_url -}}"
                    target="{{- block.settings.btn_target -}}"
                  >
                    <img
                      src="{{- block.settings.button_icon | image_url: width: auto -}}"
                      alt="{{ block.settings.image.alt }}"
                      width="{{ block.settings.image.width }}"
                      height="{{ block.settings.image.height }}"
                      class="object-cover lazy-load blur-loading"
                    >
                  </a>
                {% endif %}
            {% endcase %}
          {% endfor %}
        </div>
      </div>
      <div class="image-wrapper second-image flex flex-col justify-center items-center text-center">
        {% if section.settings.image_second -%}
          {%- liquid
            assign height = section.settings.image_second.width | divided_by: section.settings.image_second.aspect_ratio | round
            assign sizes = '100vw'
            assign widths = '375, 550, 750'
            assign fetch_priority = 'high'
            assign classes = 'image-block lazy-load blur-loading'
          -%}
          {{
            section.settings.image_second
            | image_url: width: 1200
            | image_tag:
              loading: 'lazy',
              height: height,
              sizes: sizes,
              widths: widths,
              class: classes,
              fetchpriority: fetch_priority
          }}
        {% endif %}
      </div>
    </div>
  </div>
  <div class="shape-icon-left-bottom"></div>
</div>

{% schema %}
{
  "name": "Digital health app Block",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "text",
      "id": "content_first",
      "label": "Content",
      "default": "Digital"
    },
    {
      "type": "text",
      "id": "content_second",
      "label": "Content",
      "default": "Mobile App"
    },
    {
      "type": "text",
      "id": "content_third",
      "label": "Content",
      "default": "Experience"
    },
    { "type": "header", "content": "Image block" },
    {
      "type": "image_picker",
      "id": "image_first",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_second",
      "label": "Image"
    }
  ],
  "blocks": [
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "url",
          "id": "button_url",
          "label": "Button URL",
          "info": "Enter the URL where the button should redirect when clicked."
        },
        {
          "type": "select",
          "id": "btn_target",
          "label": "Open link",
          "default": "_self",
          "options": [
            {
              "value": "_blank",
              "label": "Open in a New Tab"
            },
            {
              "value": "_self",
              "label": "Open in Same Tab"
            }
          ]
        },
        {
          "type": "image_picker",
          "id": "button_icon",
          "label": "Button icon"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Digital health app Block"
    }
  ]
}
{% endschema %}
