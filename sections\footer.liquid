<footer class="pb-4 pt-7 md:py-8 bg-white">
  <div class="container px-4 lg:px-0">
    <div class="footer-logo-section flex flex-col gap-4 justify-center items-center md:flex-row md:justify-between md:gap-6 flex-wrap">
      <div class="footer-logo-block">
        {% render 'logo-block' %}
      </div>
      <div class="copyright-block flex flex-wrap gap-2">
        {%- if section.settings.show_social -%}
          {%- render 'social-icons-list' -%}
        {%- endif -%}
      </div>
    </div>
    <hr class="my-4 border-gray-2 sm:mx-auto">
    <div class="copyright-section flex justify-center md:justify-between flex-wrap gap-4 md:gap-0">
      <div class="copyright-block">
        <p class="text-base text-gray-7">
          {{ 'general.copyright' | t }} &copy; {{ 'now' | date: '%Y' }}
          {{ 'general.styku_llc' | t }}
        </p>
      </div>
      <div class="link-block">
        <ul class="flex flex-wrap items-center gap-y-3 gap-5 md:gap-8 justify-center md:justify-end text-base text-gray-7">
          {% for link in linklists.footer.links -%}
            <li>
              <a
                class="link-item"
                href="{{ link.url }}"
                {% if link.type == 'http_link' %}
                  target="_blank"
                {% endif %}
              >
                {{- link.title -}}
              </a>
            </li>
          {%- endfor %}
        </ul>
      </div>
    </div>
  </div>
</footer>

{% schema %}
{
  "name": "t:sections.footer.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_social",
      "default": true,
      "label": "Show Social Icons"
    }
  ]
}
{% endschema %}
