{%- style -%}
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
      margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-margin {
        margin-top: {{ section.settings.margin_top }}px;
        margin-bottom: {{ section.settings.margin_bottom }}px;
      }
    }

    @media screen and (min-width: 1024px) {
      .how-it-works-items::before {
        content: '';
        position: absolute;
        z-index: -1;
        width: 496px;
        height: 598px;
        top: 116px;
        left: 50%;
        transform: translateX(-65%);
        background: url('{{ 'vector-desktop.svg' | asset_url }}');
        background-size: auto;
        background-repeat: no-repeat;
      }
    }

    .how-it-works-item::after {
      content: "";
      display: inline-block;
      position: absolute;
      -moz-border-radius: 50%;
      -webkit-border-radius: 50%;
      border-radius: 50%;
      background: radial-gradient(50% 50% at 50% 50%, #f3f8fc 52.5%, rgba(243, 248, 252, 0) 100%);
    }

    .how-it-works-item > .image-block,
    .how-it-works-item > .text-block {
      z-index: 4;
    }

    .how-it-works-item:nth-child(1) {
      display: flex;
      width: 568px;
      align-items: center;
      gap: 24px;
      top: 40px;
      position: relative;
    }

    .how-it-works-item:nth-child(1) img {
      width: 182px;
    }

    .how-it-works-item:nth-child(1) .image-mockup-icon {
      width: 162px;
      height: 159px;
      transform: rotate(-90deg);
      border-radius: 0 0 80.529px 75.036px;
      opacity: 0.4;
      background: #c2e2dd;
      position: absolute;
      top: 65px;
      left: 50px;
      z-index: -1;
    }

    .how-it-works-item:nth-child(1)::after {
      width: 512px;
      height: 504px;
      left: 165px;
    }

    .how-it-works-item:nth-child(1) .index-block {
      right: -14px;
    }

    /* Styles for the second block */
    .how-it-works-item:nth-child(2) {
      display: flex;
      width: 585px;
      align-items: center;
      gap: 16px;
      left: 625px;
      position: relative;
    }

    .how-it-works-item:nth-child(2) img {}
  .how-it-works-item:nth-child(2)::after {
    width: 458.312px;
    height: 504px;
    left: -130px;
  }

  .how-it-works-item:nth-child(2) .image-mockup-icon::after {
    content: url("{{ "half-circle.svg" | asset_url }}");
    width: 261.844px;
    height: 124.254px;
    opacity: 0.4;
    position: absolute;
    top: 40px;
    left: -70px;
    z-index: -1;
  }
  .how-it-works-item:nth-child(2) .index-block {
    right: 25px;
  }

  /* Styles for the third block */
  .how-it-works-item:nth-child(3) {
    display: flex;
    width: 582px;
    align-items: center;
    gap: 24px;
    position: relative;
  }

  .how-it-works-item:nth-child(3) img {}
  .how-it-works-item:nth-child(3)::after {
    width: 458px;
    height: 412px;
    left: 0;
  }
  .how-it-works-item:nth-child(3) .image-mockup-icon::after {
    content: url("{{ "vector-triangle.svg" | asset_url }}");
    width: 154px;
    height: 138px;
    opacity: 0.4;
    position: absolute;
    top: -33px;
    left: 75px;
    z-index: -1;
  }

  .how-it-works-item:nth-child(3) .index-block {
    right: 8px;
  }

  /* Styles for the fourth block */
  .how-it-works-item:nth-child(4) {
    display: inline-flex;
    align-items: center;
    width: 580px;
    gap: 24px;
    left: 600px;
    bottom: 50px;
    position: relative;
  }

  .how-it-works-item:nth-child(4) img {
    width: 182px;
  }

  .how-it-works-item:nth-child(4)::after {
    width: 528px;
    height: 367px;
    left: -160px;
  }
  .how-it-works-item:nth-child(4) .image-mockup-icon {
    width: 160px;
    height: 65px;
    opacity: 0.4;
    background: #dbc9da;
    position: absolute;
    left: 0;
    bottom: -25px;
    z-index: -1;
  }

  .how-it-works-item:nth-child(4) .index-block {
    right: -12px;
    bottom: -16px !important;
  }

  /* Small devices (phones, 600px and down) */
  @media only screen and (max-width: 768px) {
    .how-it-works-item {
      padding: 0 16px;
    }

    .how-it-works-item:nth-child(1)::after,
    .how-it-works-item:nth-child(2)::after,
    .how-it-works-item:nth-child(3)::after,
    .how-it-works-item:nth-child(4)::after {
      display: none;
    }

    .how-it-works-item:nth-child(1) {
      flex-direction: column;
      width: 100%;
      top: 0;
    }

    .how-it-works-item:nth-child(2) {
      flex-direction: column;
      width: 100%;
      left: 0;
      top: 0;
      overflow: hidden;
    }

    .how-it-works-item:nth-child(3) {
      flex-direction: column;
      width: 100%;
      gap: 24px;
      top: 25px;
    }

    .how-it-works-item:nth-child(4) {
      flex-direction: column;
      width: 100%;
      left: 0;
      top: 12px;
      padding-bottom: 24px;
      overflow: hidden;
    }

    .card-image-container {
      left: 0;
    }

    .vector-for-mobile {
      top: 28px;
      position: relative;
    }

    .how-it-works-item .image-block::after {
      content: '';
      position: absolute;
      width: 352px;
      height: 346.5px;
      border-radius: 100%;
      background: radial-gradient(50% 50% at 50% 50%, #F3F8FC 52.5%, rgba(243, 248, 252, 0) 100%);
      z-index: -2 !important;
    }

    .how-it-works-item:nth-child(1) .image-block::after {
      top: -100px;
      left: -50%;
      height: 328px;
    }

    .how-it-works-item:nth-child(2) .image-block::after {
      top: -50%;
      left: -32%;
    }

    .how-it-works-item:nth-child(3) .image-block::after {
      top: -90px;
      left: -28%;
    }

    .how-it-works-item:nth-child(4) .image-block::after {
      top: -65px;
      left: -50%;
    }

    .how-it-works-item::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      background-attachment: fixed;
      z-index: -10 !important;
      user-select: none;
      transform: scale(1.2);
    }

    .how-it-works-item:nth-child(1)::before {
      background-image: url('data:image/svg+xml;utf8, <svg width="535" height="178" viewBox="0 0 535 178" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 5H362C454.784 5 530 80.2162 530 173" stroke="black" stroke-width="10" stroke-linecap="round" stroke-dasharray="23 23"/></svg>');
      top: 100px;
      left: 25px;
    }

    .how-it-works-item:nth-child(2)::before {
      background-image: url('data:image/svg+xml;utf8, <svg width="535" height="351" viewBox="0 0 535 351" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M530 5C530 96.399 456.933 171.029 365.555 172.963L169.445 177.113C78.0672 179.047 5 253.677 5 345.076" stroke="black" stroke-width="10" stroke-linecap="round" stroke-dasharray="23 23"/></svg>');
      top: -18px;
    }

    .how-it-works-item:nth-child(3)::before {
      background-image: url('data:image/svg+xml;utf8, <svg width="535" height="412" viewBox="0 0 535 412" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 5V66.8702C5 160.546 80.2162 236.486 173 236.486H362C454.784 236.486 530 312.425 530 406.101" stroke="black" stroke-width="10" stroke-linecap="round" stroke-dasharray="23 23"/></svg>');
      top: -125px;
    }

    .how-it-works-item:nth-child(4)::before {
      background-image: url('data:image/svg+xml;utf8, <svg width="535" height="178" viewBox="0 0 535 178" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M530 5C530 97.78 454.784 173 362 173H5" stroke="black" stroke-width="10" stroke-linecap="round" stroke-dasharray="23 23"/></svg>');
      top: -56px;
    }

    .how-it-works-item:nth-child(2) .image-mockup-icon::after {
      content: url("{{ 'half-circle.svg' | asset_url }}");
      transform: scale(0.75);
      opacity: 0.4;
      position: absolute;
      top: 0;
      left: -50px;
      z-index: -1;
    }

    .how-it-works-item:nth-child(4) .image-mockup-icon {
      width: 170px;
      height: 65px;
      opacity: 0.4;
      background: #dbc9da;
      position: absolute;
      left: 10px;
      bottom: 47px;
      z-index: -1;
    }
  }
{%- endstyle -%}

<div class="how-it-works-section section-{{ section.id }}-margin">
  <div class="wrapper size-full py-12 pb-4 md:pt-20 md:pb-10" style="background: {{- section.settings.bg_color -}};">
    <div class="heading-block w-full md:w-1/2 text-center mx-auto mb-12 md:mb-10">
      <h1 class="section-heading-40 text-secondary font-bold">
        {{ section.settings.title }}
      </h1>
      {% if section.settings.paragraph %}
        <div class="mt-3 text-base">{{ section.settings.paragraph }}</div>
      {% endif %}
    </div>
    <div class="container section-inner-wrapper content-block overflow-hidden">
      <div class="how-it-works-items relative content-block space-y-10 md:space-y-4 z-[2] w-full">
        {%- for block in section.blocks -%}
          <div class="how-it-works-item">
            <div class="image-block flex-none relative">
              {% if block.settings.card_image -%}
                <img
                  src="{{ block.settings.card_image | image_url: width: 3840 }}"
                  alt="{{ block.settings.card_image.alt }}"
                  width="{{ block.settings.card_image.width }}"
                  height="{{ block.settings.card_image.height }}"
                  class="w-full"
                >
              {% else %}
                <div
                  role="status"
                  class="flex items-center justify-center size-44 max-w-sm bg-gray-2 rounded-lg animate-pulse"
                >
                  {{- 'icon-image-skeleton.svg' | inline_asset_content -}}
                  <span class="sr-only">Loading...</span>
                </div>
              {% endif %}
              <div class="index-block absolute bottom-0">
                <div class="flex justify-center items-center button-primary-gradient rounded-full size-[77px]">
                  <span class="text-4xl lg:text-[40px] leading-none font-bold">
                    {{ forloop.index -}}
                  </span>
                </div>
              </div>
              <div class="image-mockup-icon"></div>
            </div>
            <div class="text-block flex-1">
              <div class="content-block">
                <div class="heading-block font-bold mb-2">
                  <h2 class="text-xl text-secondary font-bold">
                    {{- block.settings.heading -}}
                  </h2>
                </div>
                <div class="text-gray-8 text-sm">{{- block.settings.description -}}</div>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const exploreBloodTests = document.getElementById('Explorebloodtests');
    exploreBloodTests?.addEventListener('click', handleExploreClick);
  });

  function handleExploreClick(event) {
    event.preventDefault();
    const sectionWrapper = event.target.closest('.shopify-section');

    const sectionHeight = sectionWrapper.offsetHeight;
    const scrolledHeight = window.scrollY;

    // Calculate the offset to scroll smoothly past the section, adding a buffer margin of 48px
    const offset = sectionHeight + {{ section.settings.section_margin_top }} - scrolledHeight;

    window.scrollBy({ top: offset, behavior: 'smooth' });
  }
</script>

{% schema %}
{
  "name": "How it Works blood tests",
  "max_blocks": 4,
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Add section title",
      "default": "How it Works"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add the subtext",
      "default": "<p>Using advanced imaging technology and anthropometric science, Healthpass accurately estimates your health risks in seconds without exposing you to unnecessary radiation</p>"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Add Image"
    },
    {
      "type": "image_picker",
      "id": "vecter_image",
      "label": "Upload svg image to show Proper"
    },
    {
      "type": "color",
      "id": "bg_color",
      "label": "Add background color",
      "default": "#F3F8FC"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "section_margin_top",
      "label": "Section margin",
      "min": 0,
      "max": 62,
      "step": 2,
      "default": 48
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 16
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 16
    }
  ],
  "blocks": [
    {
      "type": "how_it_works",
      "name": "how_it_works",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Card Heading",
          "default": "Register your testkits"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Paragraph",
          "default": "<p>Once you have your test, open the Styku mobile app and seamlessly register your test kit."
        },
        {
          "type": "image_picker",
          "id": "card_image",
          "label": "Upload image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "How it Works blood tests",
      "blocks": [
        {
          "type": "how_it_works"
        },
        {
          "type": "how_it_works"
        },
        {
          "type": "how_it_works"
        },
        {
          "type": "how_it_works"
        }
      ]
    }
  ]
}
{% endschema %}
