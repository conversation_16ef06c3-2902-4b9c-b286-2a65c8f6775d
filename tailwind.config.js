module.exports = {
  content: [
    './layout/*.liquid',
    './templates/*.liquid',
    './templates/customers*/.liquid',
    './templates/*.json',
    './sections/*.liquid',
    './snippets/*.liquid',
    './assets/*.js',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: 'var(--color-primary)',
          light: 'var(--color-primary-light)',
          muted_seafoam: 'var(--color-primary-muted-seafoam)',
          heather_blush: 'var(--color-primary-heather-blush)',
        },
        secondary: 'var(--color-secondary)',
        white: 'var(--color-white)',
        black: 'var(--color-black)',
        gray: {
          DEFAULT: 'var(--color-gray)',
          1: 'var(--color-gray-1)',
          2: 'var(--color-gray-2)',
          3: 'var(--color-gray-3)',
          4: 'var(--color-gray-4)',
          5: 'var(--color-gray-5)',
          6: 'var(--color-gray-6)',
          7: 'var(--color-gray-7)',
          8: 'var(--color-gray-8)',
          9: 'var(--color-gray-9)',
          10: 'var(--color-gray-10)',
        },
      },
      backgroundImage: {
        'gradient-primary': 'var(--gradient-primary)',
      },
      textColor: {
        'gradient-primary': 'var(--gradient-primary)',
      },
      animation: {
        'infinite-scroll': 'infinite-scroll 25s linear infinite',
      },
      keyframes: {
        'infinite-scroll': {
          from: { transform: 'translateX(0)' },
          to: { transform: 'translateX(-100%)' },
        },
      },
    },
  },
  variants: {
    extend: {},
  },
  corePlugins: {
    container: false,
  },
  plugins: [
    function ({ addComponents }) {
      addComponents({
        '.container': {
          width: '100%',
          marginLeft: 'auto',
          marginRight: 'auto',
          '@screen sm': {
            maxWidth: '100%',
          },
          '@screen md': {
            maxWidth: '100%',
          },
          '@screen lg': {
            maxWidth: '1024px',
          },
          '@screen xl': {
            maxWidth: '1216px',
          },
        },
      });
    },
    function ({ addUtilities }) {
      addUtilities({
        '.text-primary-gradient': {
          backgroundImage: 'var(--gradient-primary)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          color: 'transparent',
        },
      });
    },
  ],
};
