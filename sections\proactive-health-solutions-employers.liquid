{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  .block-main-wrapper {
    max-height: 488px;
    height: 488px;
    border-radius: 24px;
    background: #121212;
    overflow: hidden;
    position: relative;
    z-index: 1;
  }

  .proactive-health-solutions-employers-content {
    max-width: 580px;
    height: 100%;
  }

  .block-main-wrapper .text-soft-white {
    color: #fdfdfd !important;
  }

  .block-main-wrapper .text-cloud-gray {
    max-width: 85%;
  }

  .main-wrapper img.image-overflow {
    position: absolute;
    bottom: 0;
    height: 538px;
    width: auto;
    left: 62px;
    z-index: 2;
    user-select: none;
    -webkit-user-drag: none;
  }

  .block-main-wrapper .top-gradient {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 285px;
    height: 100%;
    background-color: #C8277D;
    opacity: 0.2;
    mix-blend-mode: lighten;
    filter: blur(92px);
    pointer-events: none;
    z-index: -1;
  }

  .block-main-wrapper .bottom-gradient {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 241px;
    height: 100%;
    background-color: #C8277D;
    opacity: 0.1;
    mix-blend-mode: lighten;
    filter: blur(92px);
    z-index: -1;
  }

  @media only screen and (max-width: 768px) {
    .block-main-wrapper {
      min-height: 632px;
      max-height: 700px;
      border-radius: 16px;
      padding: 16px 16px 40px
    }

    .main-wrapper img.image-overflow {
      position: absolute;
      bottom: auto;
      height: 358px;
      width: auto;
      left: 40px;
      user-select: none;
      -webkit-user-drag: none;
      top: -60px;
    }

    .block-main-wrapper .bottom-gradient {
      height: 30%;
      bottom: -60px;
      opacity: 0.2;
    }

    .block-main-wrapper .top-gradient {
      width: 200px;
      height: 55%;
    }
  }
{%- endstyle -%}
<div class="section-{{ section.id }}-margin">
  <div class="container px-4 lg:px-0">
    <div class="main-wrapper relative">
      <div class="image-wrapper">
        <div class="mobile-block-image block md:hidden">
          {% if section.settings.image -%}
            <img
              src="{{- section.settings.image | image_url: width: 720 -}}"
              alt="{{ section.settings.image.alt }}"
              width="{{ section.settings.image.width }}"
              height="{{ section.settings.image.height }}"
              class="w-full image-overflow"
            >
          {% else %}
            {{- 'image' | placeholder_svg_tag: 'w-[430px] h-[270px] border border-gray-7 rounded-xl' -}}
          {% endif %}
        </div>

        <div class="desktop-block-image hidden md:block">
          {% if section.settings.image_for_mobile -%}
            <img
              src="{{- section.settings.image_for_mobile | image_url: width: 1200 -}}"
              alt="{{ section.settings.image_for_mobile.alt }}"
              width="{{ section.settings.image_for_mobile.width }}"
              height="{{ section.settings.image_for_mobile.height }}"
              class="w-full image-overflow lazy-load blur-loading"
            >
          {% else %}
            {{- 'image' | placeholder_svg_tag: 'w-[430px] h-[270px] border border-gray-7 rounded-xl' -}}
          {% endif %}
        </div>
      </div>
      <div class="block-main-wrapper">
        <div class="overlay-gradient top-gradient"></div>
        <div class="proactive-health-solutions-employers-content md:ml-[35%]">
          <div class="flex flex-col justify-end md:justify-center h-full">
            <h2 class="heading-level-2 text-soft-white">{{- section.settings.heading -}}</h2>
            {% unless section.settings.paragraph == blank %}
              <div class="mt-6 text-base text-[#d7d7d7]">
                {{- section.settings.paragraph -}}
              </div>
            {% endunless %}
            {% unless section.settings.btn_primary_text == blank %}
              <div class="flex flex-col mt-8">
                <div class="button-block inline-flex">
                  <a
                    class="flex justify-center cursor-pointer items-center gap-4 !text-base !py-3 !px-9 button-primary-gradient"
                    href="{{ section.settings.btn_primary_url }}"
                    target="{{ section.settings.btn_primary_target }}"
                  >
                    {{ section.settings.btn_primary_text }}
                  </a>
                </div>
              </div>
            {% endunless %}
          </div>
        </div>
        <div class="overlay-gradient bottom-gradient"></div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Proactive health",
  "settings": [
    {
      "type": "header",
      "content": "Set the vertical margin"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Adjust the top margin",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0,
      "info": "Adjust the space above the section"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Adjust the bottom margin",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0,
      "info": "Adjust the space below the section"
    },
    {
      "type": "header",
      "content": "Title settings"
    },
    {
      "type": "html",
      "id": "heading",
      "label": "Heading text",
      "info": "Enter the main heading for your content block.",
      "default": "Know your Health Risks before its too late."
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Subtext",
      "info": "Enter additional text or description for your content block.",
      "default": "<p>Using advanced imaging technology and anthropometric science, Healthpass accurately estimates your health risks in seconds without exposing you to unnecessary radiation</p>"
    },
    {
      "type": "header",
      "content": "Solid button block"
    },
    {
      "type": "text",
      "id": "btn_primary_text",
      "label": "Button Text",
      "info": "Leave empty to disable the button. Enter text to display on the button."
    },
    {
      "type": "url",
      "id": "btn_primary_url",
      "label": "Button URL",
      "info": "Enter the URL where the button should redirect when clicked."
    },
    {
      "type": "select",
      "id": "btn_primary_target",
      "label": "Open link",
      "info": "Choose whether the link should open in the same or a new tab.",
      "default": "_self",
      "options": [
        {
          "value": "_blank",
          "label": "Open in a new tab"
        },
        {
          "value": "_self",
          "label": "Open in same tab"
        }
      ]
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Upload image",
      "info": "Choose an image to display in the image block."
    },
    {
      "type": "image_picker",
      "id": "image_for_mobile",
      "label": "Upload image",
      "info": "Choose an image to display in the image block for the mobile device."
    }
  ]
}
{% endschema %}
