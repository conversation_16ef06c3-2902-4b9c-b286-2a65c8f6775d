{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  .multicolumns::-webkit-scrollbar {
    width: 0 !important;
    background: transparent !important;
  }
{%- endstyle -%}

<div class="multicolumn-wrapper section-{{ section.id }}-margin {{ section.settings.desktop_section_padding_top }} {{ section.settings.desktop_section_padding_bottom }} {{ section.settings.mobile_section_padding_top }} {{ section.settings.mobile_section_padding_bottom }}">
  <div class="container px-4 md:px-0">
    {% unless section.settings.section_heading == blank %}
      <div class="flex flex-col mx-auto justify-start md:justify-center w-full md:max-w-[42rem] lg:text-left">
        <h1 class="heading-level-2 text-secondary text-center">{{- section.settings.section_heading -}}</h1>
        {% unless section.settings.paragraph == blank %}
          <div class="mt-2 text-base text-center">{{- section.settings.paragraph -}}</div>
        {% endunless %}
      </div>
    {% endunless %}
    <div class="mt-8 md:mt-12 multicolumns w-full flex md:grid md:grid-cols-4 overflow-x-auto gap-4 snap-x md:snap-none snap-mandatory">
      {% for case_study in section.settings.case_study %}
        <div class="h-auto grid grid-cols-1 gap-3 content-between border border-gray-2 rounded-3xl p-6 bg-white snap-always snap-start min-w-[85%] md:w-full">
          <div class="text-block">
            {% unless case_study.article_title == blank %}
              <div class="description mb-2 text-base text-secondary">
                {{ case_study.article_title }}
              </div>
            {% endunless %}
            {% unless case_study.journal_title == blank %}
              <p class="text-base text-gray-4">
                {{ case_study.journal_title }}
              </p>
            {% endunless %}
          </div>
          <div class="button-block">
            <a
              href="{{- case_study.pdf_file |  file_url -}}"
              target="_blank"
              class="block  gap-72 text-center rounded-full button-primary-gradient-outline py-2 px-10 text-base"
            >
              <span class="text-primary-gradient text-sm font-bold">{{- 'sections.case_study.button_text' | t -}}</span>
            </a>
          </div>
        </div>
      {% else %}
        <p class="text-base text-center col-start-1 col-end-5">Case Study Not Found!</p>
      {% endfor %}
    </div>
  </div>
</div>
{% schema %}
{
  "name": "Case study",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "section_heading",
      "label": "Section heading",
      "default": "Research Studies"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add description"
    },
    {
      "type": "header",
      "content": "Section Spacing"
    },
    {
      "type": "select",
      "id": "desktop_section_padding_top",
      "label": "Set Gap for the desktop",
      "default": "md:pt-16",
      "options": [
        { "value": "md:pt-0", "label": "PT-0" },
        { "value": "md:pt-4", "label": "PT-4" },
        { "value": "md:pt-6", "label": "PT-6" },
        { "value": "md:pt-8", "label": "PT-8" },
        { "value": "md:pt-10", "label": "PT-10" },
        { "value": "md:pt-12", "label": "PT-12" },
        { "value": "md:pt-14", "label": "PT-14" },
        { "value": "md:pt-16", "label": "PT-16" }
      ]
    },
    {
      "type": "select",
      "id": "desktop_section_padding_bottom",
      "label": "Desktop section padding bottom",
      "default": "md:pb-16",
      "options": [
        { "value": "md:pb-0", "label": "PB-0" },
        { "value": "md:pb-4", "label": "PB-4" },
        { "value": "md:pb-6", "label": "PB-6" },
        { "value": "md:pb-8", "label": "PB-8" },
        { "value": "md:pb-10", "label": "PB-10" },
        { "value": "md:pb-12", "label": "PB-12" },
        { "value": "md:pb-14", "label": "PB-14" },
        { "value": "md:pb-16", "label": "PB-16" }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_top",
      "label": "Set section padding for the Mobile",
      "default": "pt-4",
      "options": [
        { "value": "pt-0", "label": "PT-0" },
        { "value": "pt-4", "label": "PT-4" },
        { "value": "pt-6", "label": "PT-6" },
        { "value": "pt-8", "label": "PT-8" },
        { "value": "pt-10", "label": "PT-10" },
        { "value": "pt-12", "label": "PT-12" },
        { "value": "pt-14", "label": "PT-14" }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_bottom",
      "label": "Set section padding for the Mobile",
      "default": "pb-4",
      "options": [
        { "value": "pb-0", "label": "PB-0" },
        { "value": "pb-4", "label": "PB-4" },
        { "value": "pb-6", "label": "PB-6" },
        { "value": "pb-8", "label": "PB-8" },
        { "value": "pb-10", "label": "PB-10" },
        { "value": "pb-12", "label": "PB-12" },
        { "value": "pb-14", "label": "PB-14" }
      ]
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 32
    },
    {
      "type": "metaobject_list",
      "id": "case_study",
      "label": "Case study",
      "metaobject_type": "case_study",
      "limit": 16
    }
  ],
  "presets": [
    {
      "name": "Case study"
    }
  ]
}
{% endschema %}
