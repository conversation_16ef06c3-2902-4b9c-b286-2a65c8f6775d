{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  .custom-contaienr {
    padding-left: 0;
    padding-right: 0;
  }

  .custom-contaienr .custom-contaienr-content-block {
    padding:0 1rem;
  }
{%- endstyle -%}
<div
  class="media-width-text-wrapper {{ section.settings.desktop_section_padding_top_bottom }} {{ section.settings.mobile_section_padding_top_bottom }} section-{{ section.id }}-margin"
  {% unless section.settings.section_bg_color == blank %}
    style="background:{{- section.settings.section_bg_color -}};"
  {%- endunless -%}
>
  <div class="container px-4 lg:px-0 {{ section.settings.container_type -}}">
    <div
      class="flex flex-col justify-center md:justify-between {{ section.settings.mobile_container_block_gap }} {{ section.settings.desktop_container_padding_top_bottom }} {{ section.settings.mobile_container_padding_top_bottom }} {{ section.settings.mobile_container_padding_left_right }} {{ section.settings.desktop_container_padding_left_right }} {{ section.settings.container_rounded }} {{ section.settings.desktop_container_block_gap }} {% if section.settings.column  == true %}{{ section.settings.media_align_desktop -}}{% endif %}"
      style="{%- if section.settings.box_shadow %}box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);{% endif %}{% if section.settings.container_bg_color != blank %}background:{{- section.settings.container_bg_color -}}{% endif %}"
    >
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'content_block' %}
            {% liquid
              assign content_width = block.settings.content_block_width
              case content_width
                when 'md:w-1/5'
                  assign image_width = 'md:w-4/5'
                when 'md:w-1/4'
                  assign image_width = 'md:w-3/4'
                when 'md:w-1/3'
                  assign image_width = 'md:w-2/3'
                when 'md:w-2/5'
                  assign image_width = 'md:w-3/5'
                when 'md:w-1/2'
                  assign image_width = 'md:w-1/2'
                when 'md:w-3/5'
                  assign image_width = 'md:w-2/5'
                when 'md:w-2/3'
                  assign image_width = 'md:w-1/3'
                when 'md:w-3/4'
                  assign image_width = 'md:w-1/4'
                when 'md:w-4/5'
                  assign image_width = 'md:w-1/5'
                when 'md:w-full'
                  assign image_width = 'md:w-full'
                when 'md:w-auto'
                  assign image_width = 'md:w-auto'
              endcase
            %}
            <div class="{{ content_width }} flex flex-col justify-start md:justify-center {{ section.settings.container_type | append: '-content-block'}} {{ block.settings.text_alignment -}}">
              <h1 class="{{ block.settings.title_font_size }} text-secondary">{{- block.settings.heading -}}</h1>
              <div class="mt-3 text-base">{{- block.settings.paragraph -}}</div>
              {% unless block.settings.btn_primary_text == blank %}
                <div class="flex flex-col mt-7">
                  <div class="button-block inline-flex {% if block.settings.text_alignment == 'text-center' %}justify-center{% else %} justify-start{% endif %}">
                    <a
                      class="flex justify-center cursor-pointer items-center gap-4 !text-base !py-3 !px-9 button-primary-gradient"
                      href="{{ block.settings.btn_primary_url }}"
                      target="{{ block.settings.btn_primary_target }}"
                    >
                      {{ block.settings.btn_primary_text }}
                    </a>
                  </div>
                </div>
              {% endunless %}
            </div>
          {% when 'image_block' %}
            <div class="{{ image_width }} {{ block.settings.image_order }} md:order-none flex items-center justify-center {{ section.settings.container_type | append: '-image-block'}}">
              {%- liquid
                assign sizes = '100vw'
                assign widths = '550, 750, 1100, 1500, 1780'
                assign fetch_priority = 'auto'
                if section.index == 1
                  assign fetch_priority = 'high'
                endif
              -%}
              {% if block.settings.image_mobile != blank %}
                {{
                  block.settings.image_mobile
                  | image_url: width: 3840
                  | image_tag:
                    loading: 'lazy',
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority,
                    class: 'w-full block md:hidden lazy-load blur-loading'
                }}
              {% endif %}
              {%- if block.settings.image -%}
                {%- liquid
                  assign image_class = 'w-full lazy-load blur-loading'
                  if block.settings.image_mobile == blank
                    assign image_class = 'w-full block lazy-load blur-loading'
                  else
                    assign image_class = 'w-full hidden md:block lazy-load blur-loading'
                  endif
                -%}
                {{
                  block.settings.image
                  | image_url: width: 3840
                  | image_tag:
                    loading: 'lazy',
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority,
                    class: image_class
                }}
              {%- else -%}
                {{- 'image' | placeholder_svg_tag: 'w-[430px] h-[270px] border border-gray-7 rounded-xl' -}}
              {%- endif -%}
            </div>
        {% endcase %}
      {% endfor %}
    </div>
    {% unless section.settings.btn_primary_text == blank %}
      <div class="flex flex-col mt-7 md:mt-10">
        <div class="button-block inline-flex justify-center">
          <a
            class="flex justify-center cursor-pointer items-center gap-4 !text-sm !py-3 button-primary-gradient"
            href="{{ section.settings.btn_primary_url }}"
            target="{{ section.settings.btn_primary_target }}"
          >
            {{ section.settings.btn_primary_text }}
          </a>
        </div>
      </div>
    {% endunless %}
  </div>
</div>

{% schema %}
{
  "name": "Media with text",
  "settings": [
    {
      "type": "header",
      "content": "General settings for section"
    },
    {
      "type": "color_background",
      "id": "section_bg_color",
      "label": "Section background",
      "info": "Choose the background color for the section"
    },
    {
      "type": "select",
      "id": "desktop_section_padding_top_bottom",
      "label": "Set the vertical padding (Desktop)",
      "info": "Set the vertical padding for desktop devices",
      "default": "md:py-16",
      "options": [
        {
          "value": "md:py-0",
          "label": "None"
        },
        {
          "value": "md:py-4",
          "label": "PY-4"
        },
        {
          "value": "md:py-6",
          "label": "PY-6"
        },
        {
          "value": "md:py-8",
          "label": "PY-8"
        },
        {
          "value": "md:py-10",
          "label": "PY-10"
        },
        {
          "value": "md:py-12",
          "label": "PY-12"
        },
        {
          "value": "md:py-14",
          "label": "PY-14"
        },
        {
          "value": "md:py-16",
          "label": "PY-16"
        },
        {
          "value": "md:py-20",
          "label": "PY-20"
        },
        {
          "value": "md:py-24",
          "label": "PY-24"
        },
        {
          "value": "md:py-28",
          "label": "PY-28"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_top_bottom",
      "label": "Set the vertical padding (Mobile)",
      "info": "Set the vertical padding for mobile devices",
      "default": "py-4",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-4",
          "label": "PY-4"
        },
        {
          "value": "py-6",
          "label": "PY-6"
        },
        {
          "value": "py-8",
          "label": "PY-8"
        },
        {
          "value": "py-10",
          "label": "PY-10"
        },
        {
          "value": "py-12",
          "label": "PY-12"
        },
        {
          "value": "py-14",
          "label": "PY-14"
        }
      ]
    },
    {
      "type": "header",
      "content": "Set the vertical margin"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Adjust the top margin",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0,
      "info": "Adjust the space above the section"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Adjust the bottom margin",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0,
      "info": "Adjust the space below the section"
    },
    {
      "type": "header",
      "content": "General settings for the container"
    },
    {
      "type": "color_background",
      "id": "container_bg_color",
      "label": "Container background settings",
      "info": "Choose the background color for the container"
    },
    {
      "type": "checkbox",
      "id": "column",
      "label": "Set container to two columns",
      "info": "This will arrange the container into two columns. Check to enable this layout.",
      "default": true
    },
    {
      "type": "select",
      "id": "container_type",
      "label": "Media alignment on desktop",
      "info": "This option applies only when the container is set to two columns. Choose the alignment for the media on desktop.",
      "default": "standard-container",
      "options": [
        {
          "value": "custom-contaienr",
          "label": "Custom contaienr"
        },
        {
          "value": "standard-container",
          "label": "Standard contaienr"
        }
      ]
    },
    {
      "type": "select",
      "id": "media_align_desktop",
      "label": "Media alignment on desktop",
      "info": "This option applies only when the container is set to two columns. Choose the alignment for the media on desktop.",
      "default": "md:flex-row",
      "options": [
        {
          "value": "md:flex-row",
          "label": "Left"
        },
        {
          "value": "md:flex-row-reverse",
          "label": "Right"
        }
      ]
    },
    {
      "type": "select",
      "id": "desktop_container_block_gap",
      "label": "Set container gap on desktop",
      "info": "Set the gap between blocks inside the container on desktop devices",
      "default": "md:gap-4",
      "options": [
        {
          "value": "md:gap-0",
          "label": "None"
        },
        {
          "value": "md:gap-4",
          "label": "Gap-4"
        },
        {
          "value": "md:gap-6",
          "label": "Gap-6"
        },
        {
          "value": "md:gap-8",
          "label": "Gap-8"
        },
        {
          "value": "md:gap-10",
          "label": "Gap-10"
        },
        {
          "value": "md:gap-12",
          "label": "Gap-12"
        },
        {
          "value": "md:gap-14",
          "label": "Gap-14"
        },
        {
          "value": "md:gap-16",
          "label": "Gap-16"
        },
        {
          "value": "md:gap-20",
          "label": "Gap-20"
        },
        {
          "value": "md:gap-24",
          "label": "Gap-24"
        },
        {
          "value": "md:gap-28",
          "label": "Gap-28"
        },
        {
          "value": "md:gap-32",
          "label": "Gap-32"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_container_block_gap",
      "label": "Set gap for container block on mobile",
      "info": "Set the gap between blocks inside the container on mobile devices",
      "default": "gap-4",
      "options": [
        {
          "value": "gap-0",
          "label": "None"
        },
        {
          "value": "gap-4",
          "label": "Gap-4"
        },
        {
          "value": "gap-6",
          "label": "Gap-6"
        },
        {
          "value": "gap-8",
          "label": "Gap-8"
        },
        {
          "value": "gap-10",
          "label": "Gap-10"
        },
        {
          "value": "gap-12",
          "label": "Gap-12"
        },
        {
          "value": "gap-14",
          "label": "Gap-14"
        },
        {
          "value": "gap-16",
          "label": "Gap-16"
        },
        {
          "value": "gap-20",
          "label": "Gap-20"
        },
        {
          "value": "gap-24",
          "label": "Gap-24"
        },
        {
          "value": "gap-28",
          "label": "Gap-28"
        },
        {
          "value": "gap-32",
          "label": "Gap-32"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "box_shadow",
      "label": "Add container box shadow",
      "default": false,
      "info": "Enable to add a shadow effect to the container"
    },
    {
      "type": "select",
      "id": "container_rounded",
      "label": "Rounded corner",
      "info": "Choose the rounded corner size for the container",
      "default": "rounded-none",
      "options": [
        {
          "value": "rounded-none",
          "label": "None"
        },
        {
          "value": "rounded-sm",
          "label": "SM"
        },
        {
          "value": "rounded-md",
          "label": "MD"
        },
        {
          "value": "rounded-lg",
          "label": "LG"
        },
        {
          "value": "rounded-xl",
          "label": "XL"
        },
        {
          "value": "rounded-2xl",
          "label": "2XL"
        },
        {
          "value": "rounded-3xl",
          "label": "3XL"
        }
      ]
    },
    {
      "type": "select",
      "id": "desktop_container_padding_top_bottom",
      "label": "Set container padding for desktop",
      "info": "Set the top and bottom padding for the container on desktop devices",
      "default": "md:py-16",
      "options": [
        {
          "value": "md:py-0",
          "label": "None"
        },
        {
          "value": "md:py-4",
          "label": "PY-4"
        },
        {
          "value": "md:py-6",
          "label": "PY-6"
        },
        {
          "value": "md:py-8",
          "label": "PY-8"
        },
        {
          "value": "md:py-10",
          "label": "PY-10"
        },
        {
          "value": "md:py-12",
          "label": "PY-12"
        },
        {
          "value": "md:py-14",
          "label": "PY-14"
        },
        {
          "value": "md:py-16",
          "label": "PY-16"
        },
        {
          "value": "md:py-20",
          "label": "PY-20"
        },
        {
          "value": "md:py-24",
          "label": "PY-24"
        },
        {
          "value": "md:py-28",
          "label": "PY-28"
        }
      ]
    },
    {
      "type": "select",
      "id": "desktop_container_padding_left_right",
      "label": "Set left right Container padding for desktop",
      "info": "Set the left and right padding for the container on desktop devices",
      "default": "md:px-16",
      "options": [
        {
          "value": "md:px-0",
          "label": "None"
        },
        {
          "value": "md:px-4",
          "label": "PX-4"
        },
        {
          "value": "md:px-6",
          "label": "PX-6"
        },
        {
          "value": "md:px-8",
          "label": "PX-8"
        },
        {
          "value": "md:px-10",
          "label": "PX-10"
        },
        {
          "value": "md:px-12",
          "label": "PX-12"
        },
        {
          "value": "md:px-14",
          "label": "PX-14"
        },
        {
          "value": "md:px-16",
          "label": "PX-16"
        },
        {
          "value": "md:px-20",
          "label": "PX-20"
        },
        {
          "value": "md:px-24",
          "label": "PX-24"
        },
        {
          "value": "md:px-28",
          "label": "PX-28"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_container_padding_top_bottom",
      "label": "Set top bottom container padding for mobile",
      "info": "Set the top and bottom padding for the container on mobile devices",
      "default": "py-4",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-4",
          "label": "PY-4"
        },
        {
          "value": "py-6",
          "label": "PY-6"
        },
        {
          "value": "py-8",
          "label": "PY-8"
        },
        {
          "value": "py-10",
          "label": "PY-10"
        },
        {
          "value": "py-12",
          "label": "PY-12"
        },
        {
          "value": "py-14",
          "label": "PY-14"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_container_padding_left_right",
      "label": "Set padding for the left and right of the container on mobile devices",
      "default": "px-4",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-4",
          "label": "PX-4"
        },
        {
          "value": "px-6",
          "label": "PX-6"
        },
        {
          "value": "px-8",
          "label": "PX-8"
        },
        {
          "value": "px-10",
          "label": "PX-10"
        },
        {
          "value": "px-12",
          "label": "PX-12"
        },
        {
          "value": "px-14",
          "label": "PX-14"
        }
      ]
    },
    {
      "type": "header",
      "content": "Solid button block"
    },
    {
      "type": "text",
      "id": "btn_primary_text",
      "label": "Button text",
      "default": "Primary button",
      "info": "Leave empty to disable the button. Enter text to display on the button."
    },
    {
      "type": "url",
      "id": "btn_primary_url",
      "label": "Btton URL",
      "info": "Enter the URL where the button should redirect when clicked."
    },
    {
      "type": "select",
      "id": "btn_primary_target",
      "label": "Open link",
      "info": "Choose whether the link should open in the same or a new tab.",
      "default": "_self",
      "options": [
        {
          "value": "_blank",
          "label": "Open in a new tab"
        },
        {
          "value": "_self",
          "label": "Open in same tab"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "content_block",
      "name": "Content block",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "content_block_width",
          "label": "Content block width",
          "info": "Set the width of the content block on medium-sized screens and larger.",
          "default": "md:w-1/2",
          "options": [
            {
              "value": "md:w-auto",
              "label": "Auto"
            },
            {
              "value": "md:w-1/5",
              "label": "20%"
            },
            {
              "value": "md:w-1/4",
              "label": "25%"
            },
            {
              "value": "md:w-1/3",
              "label": "33.33%"
            },
            {
              "value": "md:w-2/5",
              "label": "40%"
            },
            {
              "value": "md:w-1/2",
              "label": "50%"
            },
            {
              "value": "md:w-3/5",
              "label": "60%"
            },
            {
              "value": "md:w-2/3",
              "label": "66.66%"
            },

            {
              "value": "md:w-3/4",
              "label": "75%"
            },
            {
              "value": "md:w-4/5",
              "label": "80%"
            },
            {
              "value": "md:w-full",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Align text",
          "info": "Select the text alignment for the content block.",
          "default": "text-left",
          "options": [
            {
              "value": "text-left",
              "label": "Left"
            },
            {
              "value": "text-center",
              "label": "Center"
            },
            {
              "value": "text-right",
              "label": "Right"
            },
            {
              "value": "text-justify",
              "label": "Justify"
            }
          ]
        },
        {
          "type": "header",
          "content": "Title settings"
        },
        {
          "type": "html",
          "id": "heading",
          "label": "Heading text",
          "info": "Enter the main heading for your content block.",
          "default": "Know your Health Risks before its too late."
        },
        {
          "type": "select",
          "id": "title_font_size",
          "label": "Title font size",
          "info": "Choose the font size for the title (Heading).",
          "default": "heading-level-2",
          "options": [
            {
              "value": "heading-level-1",
              "label": "Level 1"
            },
            {
              "value": "heading-level-2",
              "label": "Level 2"
            },
            {
              "value": "heading-level-3",
              "label": "Level 3"
            },
            {
              "value": "heading-level-4",
              "label": "Level 4"
            }
          ]
        },
        {
          "type": "richtext",
          "id": "paragraph",
          "label": "Subtext",
          "info": "Enter additional text or description for your content block.",
          "default": "<p>Using advanced imaging technology and anthropometric science, Healthpass accurately estimates your health risks in seconds without exposing you to unnecessary radiation</p>"
        },
        {
          "type": "header",
          "content": "Solid button block"
        },
        {
          "type": "text",
          "id": "btn_primary_text",
          "label": "Button Text",
          "info": "Leave empty to disable the button. Enter text to display on the button."
        },
        {
          "type": "url",
          "id": "btn_primary_url",
          "label": "Button URL",
          "info": "Enter the URL where the button should redirect when clicked."
        },
        {
          "type": "select",
          "id": "btn_primary_target",
          "label": "Open link",
          "info": "Choose whether the link should open in the same or a new tab.",
          "default": "_self",
          "options": [
            {
              "value": "_blank",
              "label": "Open in a new tab"
            },
            {
              "value": "_self",
              "label": "Open in same tab"
            }
          ]
        }
      ]
    },
    {
      "type": "image_block",
      "name": "Image block",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Upload image",
          "info": "Choose an image to display in the image block."
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Image - mobile"
        },
        {
          "type": "select",
          "id": "image_order",
          "label": "Set the image order on mobile",
          "info": "Determine the order in which the image appears on mobile devices.",
          "default": "order-none",
          "options": [
            {
              "value": "order-none",
              "label": "Order none"
            },
            {
              "value": "order-first",
              "label": "Order first"
            },
            {
              "value": "order-last",
              "label": "Order last"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Media with text",
      "blocks": [
        {
          "type": "content_block"
        },
        {
          "type": "image_block"
        }
      ]
    }
  ]
}
{% endschema %}
