{% if template == 'index'
  or template.name == 'product'
  or template == 'page.partners'
  or template == 'page.body-scans'
%}
  <script src="{{ 'jquery-1.12.4.min.js' | asset_url }}"></script>
  <script src="{{ 'owl.carousel.min.js' | asset_url }}"></script>
  <script>
    // Testimonial slider
    const testimonialSlider = $('.testimonial-slider');
    $('.testimonial-slider').owlCarousel({
      center: true,
      items: 1.22,
      loop: true,
      dots: false,
      margin: 24,
      responsive: {
        600: {
          items: 3.5,
        },
      },
    });

    $('.slideshow-styku-academy').owlCarousel({
      stagePadding: 64,
      loop: false,
      items: 1,
      lazyLoad: true,
      autoplay: false,
      margin: 24,
    });

    // Product thumbnail slider
    var featuredImage = $('#featuredImage');
    var thumbsImage = $('#thumbsImage');
    var syncedSecondary = true;

    featuredImage
      .owlCarousel({
        items: 1,
        slideSpeed: 800,
        margin: 12,
        nav: false,
        autoplay: false,
        dots: false,
        loop: false,
        responsiveRefreshRate: 200,
      })
      .on('changed.owl.carousel', syncPosition);

    thumbsImage
      .on('initialized.owl.carousel', function () {
        thumbsImage.find('.owl-item').eq(0).addClass('current');
      })
      .owlCarousel({
        items: 3,
        dots: false,
        loop: false,
        margin: 12,
        smartSpeed: 800,
        slideSpeed: 200,
        slideBy: 1,
        responsiveRefreshRate: 200,
        nav: true,
        navText: [
          '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none"><path d="M11.3333 14.8503L5.33333 8.85034L11.3333 2.85034" stroke="#7E7E7E" stroke-width="0.8"/></svg>',
          '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none"><path d="M4.66666 14.8503L10.6667 8.85034L4.66666 2.85034" stroke="#7E7E7E" stroke-width="0.8"/></svg>',
        ],
      })
      .on('changed.owl.carousel', syncPosition2);

    function syncPosition(el) {
      var current = el.item.index;

      thumbsImage.find('.owl-item').removeClass('current').eq(current).addClass('current');
      var onscreen = thumbsImage.find('.owl-item.active').length - 1;
      var start = thumbsImage.find('.owl-item.active').first().index();
      var end = thumbsImage.find('.owl-item.active').last().index();

      if (current > end) {
        thumbsImage.data('owl.carousel').to(current, 100, true);
      }
      if (current < start) {
        thumbsImage.data('owl.carousel').to(current - onscreen, 100, true);
      }
    }

    function syncPosition2(el) {
      if (syncedSecondary) {
        var number = el.item.index;
        featuredImage.data('owl.carousel').to(number, 100, true);
      }
    }

    thumbsImage.on('click', '.owl-item', function (e) {
      e.preventDefault();
      var number = $(this).index();
      featuredImage.data('owl.carousel').to(number, 300, true);
    });
  </script>
{% endif %}
