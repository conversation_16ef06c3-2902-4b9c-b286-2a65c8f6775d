<div class="grid grid-cols-1 {{ classes }} gap-6">
  {% assign sorted_products = product | sort: 'title' %}
  {%- for product in sorted_products -%}
    <a
      href="{{ product.url }}"
      role="link"
      class="relative flex w-full flex-col overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-md justify-between"
    >
      <div class="content-block">
        <div class="flex overflow-hidden">
          <img
            srcset="
              {%- if product.featured_media.width >= 165 -%}{{ product.featured_media | image_url: width: 165 }} 165w,{%- endif -%}
              {%- if product.featured_media.width >= 360 -%}{{ product.featured_media | image_url: width: 360 }} 360w,{%- endif -%}
              {%- if product.featured_media.width >= 533 -%}{{ product.featured_media | image_url: width: 533 }} 533w,{%- endif -%}
              {%- if product.featured_media.width >= 720 -%}{{ product.featured_media | image_url: width: 720 }} 720w,{%- endif -%}
              {%- if product.featured_media.width >= 940 -%}{{ product.featured_media | image_url: width: 940 }} 940w,{%- endif -%}
              {%- if product.featured_media.width >= 1066 -%}{{ product.featured_media | image_url: width: 1066 }} 1066w,{%- endif -%}
            "
            src="{{ product.featured_media | image_url: width: 533 }}"
            alt="{{ product.featured_media.alt | escape }}"
            loading="lazy"
            width="{{ product.featured_media.width }}"
            height="{{ product.featured_media.height }}"
          >
        </div>
        <div class="p-4 flex flex-col gap-2">
          <h3 class="heading-level-4">{{ product.title }}</h3>
          <p class="leading-none flex items-center gap-2">
            <span class="text-base font-bold text-primary-gradient">
              {{ product.price | money_without_trailing_zeros -}}
            </span>
            {% if product.compare_at_price %}
              <span class="text-base font-bold !text-gray-3 line-through">
                {{ product.compare_at_price | money_without_trailing_zeros -}}
              </span>
            {% endif %}
            {% if product.type == 'health-products' %}
              <span class="p-1 bg-[#53cc2824] flex gap-2 rounded-md">
                {%- render 'green-check-mark-icon' %}
                <span class="text-xs text-[#53CC28]">{{ 'sections.health_product.badge' | t }}</span>
              </span>
            {% endif %}
          </p>
          <p class="text-sm text-gray-8">{{ product.description }}</p>
          {% if product.metafields.vital.what_is_tested %}
            <div class="tags-block mb-2">
              <p class="text-xs text-gray-7 mb-1">
                {{ 'sections.health_product.custom_tags_title' | t }}
              </p>
              <div class="select-none flex flex-wrap gap-1">
                {% for item in product.metafields.vital.what_is_tested.value %}
                  <span
                    class="py-0.5 px-2 rounded-full text-xs bg-[#EBEBEB] text-secondary"
                  >
                    {{ item.whats_tested }}
                  </span>
                {% endfor %}
              </div>
            </div>
          {% endif %}
        </div>
      </div>
      <div class="px-4 pb-4">
        {% render 'product-form', product: product %}
      </div>
    </a>
  {%- endfor -%}
</div>
