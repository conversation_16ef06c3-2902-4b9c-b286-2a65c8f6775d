document.addEventListener('DOMContentLoaded', function () {
  const exceptionMessage = window.errorMessage;
  const form = {
    scheduleScanForm: document.getElementById('scheduleScanForm'),
    inputs: {
      firstName: document.getElementById('firstName'),
      lastName: document.getElementById('lastName'),
      email: document.getElementById('email'),
      phoneNumber: document.getElementById('phoneNumber'),
      checkbox: document.getElementById('checkbox'),
    },
    submitButton: document.getElementById('submitButton'),
    spinnerIcon: document.getElementById('spinnerIcon'),
    phoneNumberError: document.getElementById('phoneNumberErrorMessage'),
    invalidEmailErrorMessage: document.getElementById('invalidEmailErrorMessage'),
    requiredEmailErrorMessage: document.getElementById('requiredEmailErrorMessage'),
  };

  const intlTelUtilsScript = 'https://cdn.jsdelivr.net/npm/intl-tel-input@19.2.16/build/js/utils.js';

  // Initializes intlTelInput plugin for phone number input
  const iti = window.intlTelInput(form.inputs.phoneNumber, {
    initialCountry: 'us',
    utilsScript: intlTelUtilsScript,
  });

  function isRequired(value) {
    return value.trim().length > 0;
  }

  function isValidPhone(input) {
    return iti.isValidNumber(input);
  }

  // Function to validate the entire form
  const enableSubmit = () => {
    const {
      inputs: { firstName, lastName, email, phoneNumber, checkbox },
      submitButton,
    } = form;

    // Disable submit button if any of the conditions are not met
    submitButton.disabled = !(
      isRequired(firstName.value) &&
      isRequired(lastName.value) &&
      isEmailValid(email.value) &&
      isValidPhone(phoneNumber.value) &&
      checkbox.checked
    );
  };

  // Function to add event listeners to input fields
  const addInputListeners = () => {
    const { inputs } = form;
    Object.values(inputs).forEach((input) => {
      input.addEventListener('input', enableSubmit);

      if (input === inputs.email) {
        input.addEventListener('input', validateEmail);
      }

      if (input === inputs.phoneNumber) {
        input.addEventListener('input', validatePhoneNumberInput);
      }
    });
  };

  // Function to validate phone number input
  function validatePhoneNumberInput() {
    const {
      inputs: { phoneNumber },
      phoneNumberError,
    } = form;

    if (isValidPhone(phoneNumber.value)) {
      phoneNumber.classList.remove('error');
      phoneNumberError.classList.add('hidden');
    } else {
      phoneNumber.classList.add('error');
      phoneNumberError.classList.remove('hidden');
    }
  }

  // Function to validate email input
  const validateEmail = () => {
    const {
      inputs: { email },
      requiredEmailErrorMessage,
      invalidEmailErrorMessage,
    } = form;

    if (email.value.trim() === '') {
      email.classList.add('error');
      invalidEmailErrorMessage.classList.add('hidden');
      requiredEmailErrorMessage.classList.remove('hidden');
    } else if (!isEmailValid(email.value)) {
      email.classList.add('error');
      invalidEmailErrorMessage.classList.remove('hidden');
      requiredEmailErrorMessage.classList.add('hidden');
    } else {
      email.classList.remove('error');
      requiredEmailErrorMessage.classList.add('hidden');
      invalidEmailErrorMessage.classList.add('hidden');
    }
  };

  // Function to handle form submission
  async function handleSubmit(event) {
    event.preventDefault();
    const { submitButton, spinnerIcon } = form;

    submitButton.disabled = true;
    spinnerIcon.classList.remove('hidden');

    const formData = new FormData(form.scheduleScanForm);
    const requestBody = {};
    formData.forEach((value, key) => {
      requestBody[key] = value;
    });

    try {
      const scheduleScanPayload = API_PAYLOADS.SCHEDULE_SCAN;
      scheduleScanPayload.body = JSON.stringify(requestBody);

      const scheduleScanResponse = await fetchData(scheduleScanPayload);

      if (scheduleScanResponse.ok) {
        const scheduleScanResponseData = await scheduleScanResponse.json();

        switch (scheduleScanResponseData.status) {
          case SUCCESS_CODE.SUCCESS:
            document.getElementById('scheduleScanSuccessMessage').classList.remove('hidden');
            form.scheduleScanForm.closest('.schedule-scan-form-block').classList.add('hidden');
            form.scheduleScanForm.reset();
            break;
          case ERROR_CODES.USER_NOT_FOUND:
            showToastMessage(exceptionMessage.userNotFound);
            break;
          case ERROR_CODES.UNABLE_TO_SEND_EMAIL:
            showToastMessage(exceptionMessage.unableToSendEmail);
            break;
          default:
            showToastMessage(exceptionMessage.failedToScheduleScan);
            break;
        }
      } else {
        showToastMessage(exceptionMessage.somethingWentWrong);
      }
    } catch (error) {
      showToastMessage(exceptionMessage.somethingWentWrong);
      console.error('Error:', error);
    } finally {
      submitButton.disabled = false;
      spinnerIcon.classList.add('hidden');
    }
  }

  addInputListeners();
  form.scheduleScanForm.addEventListener('submit', handleSubmit);
});
