// Method to handle modal triggers
const modalTriggers = document.querySelectorAll('[data-toggle="modal-selector"]');
modalTriggers.forEach(function (trigger) {
    trigger.addEventListener("click", function () {
        const target = this.getAttribute("data-target");
        const modal = document.querySelector('[data-modal="' + target + '"]');
        const childElement = modal.firstElementChild;
        modal.classList.add('active');
        childElement.classList.add('active');
        document.body.style.overflowY = 'hidden';
    });
});

// Method to handle modal triggers close buttons
const closeButtons = document.querySelectorAll('[data-dismiss="modal-selector"]');
closeButtons.forEach(function (button) {
    button.addEventListener("click", function () {
        const modal = this.closest(".modal-overlay");
        const childElement = modal.firstElementChild;
        modal.classList.remove('active');
        childElement.classList.remove('active');
        document.body.style.overflowY = '';
    });
});
