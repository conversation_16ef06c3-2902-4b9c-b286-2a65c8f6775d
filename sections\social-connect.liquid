<div class="container px-4 lg:px-0 mb-20 hidden">
  <div class="flex flex-col md:flex-row gap-6">
    <!-- Instagram Card -->
    <div
      class="grid items-stretch content-between p-6 rounded-2xl md:w-1/2 h-56 "
      style="
        background:
          url('https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Group_2_1.png?v=1726235738');
        background-size: cover; /* Ensure the image covers the element */
        background-position: center;
      "
    >
      <div class="heading-block">
        <h2 class="text-3xl font-bold">Connect with us on <span class="text-primary-gradient">Instagram</span></h2>
      </div>
      <div class="link-block flex gap-2">
        <div class="icon-block">
          <svg xmlns="http://www.w3.org/2000/svg" width="27" height="26" viewBox="0 0 27 26" fill="none">
            <g clip-path="url(#clip0_7998_27598)">
              <path d="M13.3148 2.5544C16.7192 2.5544 17.1188 2.56957 18.4644 2.63028C19.7088 2.68592 20.3816 2.89332 20.8318 3.07037C21.4287 3.30307 21.8536 3.57623 22.2988 4.02138C22.744 4.46654 23.0222 4.89145 23.2498 5.48836C23.4218 5.93858 23.6343 6.61136 23.6899 7.85577C23.7506 9.20135 23.7658 9.60097 23.7658 13.0054C23.7658 16.4098 23.7506 16.8094 23.6899 18.155C23.6343 19.3994 23.4269 20.0722 23.2498 20.5224C23.0171 21.1193 22.744 21.5442 22.2988 21.9894C21.8536 22.4345 21.4287 22.7128 20.8318 22.9404C20.3816 23.1124 19.7088 23.3249 18.4644 23.3805C17.1188 23.4412 16.7192 23.4564 13.3148 23.4564C9.91038 23.4564 9.51075 23.4412 8.16517 23.3805C6.92076 23.3249 6.24798 23.1175 5.79776 22.9404C5.20085 22.7077 4.77593 22.4345 4.33078 21.9894C3.88563 21.5442 3.6074 21.1193 3.37977 20.5224C3.20778 20.0722 2.99532 19.3994 2.93967 18.155C2.87897 16.8094 2.8638 16.4098 2.8638 13.0054C2.8638 9.60097 2.87897 9.20135 2.93967 7.85577C2.99532 6.61136 3.20272 5.93858 3.37977 5.48836C3.61246 4.89145 3.88563 4.46654 4.33078 4.02138C4.77593 3.57623 5.20085 3.29801 5.79776 3.07037C6.24798 2.89838 6.92076 2.68592 8.16517 2.63028C9.51075 2.56452 9.91543 2.5544 13.3148 2.5544ZM13.3148 0.257812C9.85473 0.257812 9.41969 0.272988 8.05894 0.333691C6.70325 0.394394 5.77753 0.611912 4.96816 0.925543C4.12844 1.24929 3.42024 1.68939 2.71204 2.39758C2.00384 3.10578 1.5688 3.81904 1.24 4.6537C0.926365 5.46307 0.708847 6.38879 0.648144 7.74954C0.587441 9.10524 0.572266 9.54027 0.572266 13.0003C0.572266 16.4604 0.587441 16.8954 0.648144 18.2562C0.708847 19.6119 0.926365 20.5376 1.24 21.352C1.56374 22.1917 2.00384 22.8999 2.71204 23.6081C3.42024 24.3163 4.1335 24.7514 4.96816 25.0802C5.77753 25.3938 6.70325 25.6113 8.064 25.672C9.42475 25.7327 9.85473 25.7479 13.3198 25.7479C16.785 25.7479 17.2149 25.7327 18.5757 25.672C19.9314 25.6113 20.8571 25.3938 21.6715 25.0802C22.5113 24.7564 23.2195 24.3163 23.9277 23.6081C24.6359 22.8999 25.0709 22.1867 25.3997 21.352C25.7133 20.5426 25.9309 19.6169 25.9916 18.2562C26.0523 16.8954 26.0674 16.4654 26.0674 13.0003C26.0674 9.53521 26.0523 9.10524 25.9916 7.74448C25.9309 6.38879 25.7133 5.46307 25.3997 4.64864C25.076 3.80892 24.6359 3.10072 23.9277 2.39253C23.2195 1.68433 22.5062 1.24929 21.6715 0.920484C20.8622 0.606853 19.9365 0.389335 18.5757 0.328632C17.2099 0.272988 16.7748 0.257812 13.3148 0.257812Z" fill="#3D3D3D"/>
              <path d="M13.3148 2.5544C16.7192 2.5544 17.1188 2.56957 18.4644 2.63028C19.7088 2.68592 20.3816 2.89332 20.8318 3.07037C21.4287 3.30307 21.8536 3.57623 22.2988 4.02138C22.744 4.46654 23.0222 4.89145 23.2498 5.48836C23.4218 5.93858 23.6343 6.61136 23.6899 7.85577C23.7506 9.20135 23.7658 9.60097 23.7658 13.0054C23.7658 16.4098 23.7506 16.8094 23.6899 18.155C23.6343 19.3994 23.4269 20.0722 23.2498 20.5224C23.0171 21.1193 22.744 21.5442 22.2988 21.9894C21.8536 22.4345 21.4287 22.7128 20.8318 22.9404C20.3816 23.1124 19.7088 23.3249 18.4644 23.3805C17.1188 23.4412 16.7192 23.4564 13.3148 23.4564C9.91038 23.4564 9.51075 23.4412 8.16517 23.3805C6.92076 23.3249 6.24798 23.1175 5.79776 22.9404C5.20085 22.7077 4.77593 22.4345 4.33078 21.9894C3.88563 21.5442 3.6074 21.1193 3.37977 20.5224C3.20778 20.0722 2.99532 19.3994 2.93967 18.155C2.87897 16.8094 2.8638 16.4098 2.8638 13.0054C2.8638 9.60097 2.87897 9.20135 2.93967 7.85577C2.99532 6.61136 3.20272 5.93858 3.37977 5.48836C3.61246 4.89145 3.88563 4.46654 4.33078 4.02138C4.77593 3.57623 5.20085 3.29801 5.79776 3.07037C6.24798 2.89838 6.92076 2.68592 8.16517 2.63028C9.51075 2.56452 9.91543 2.5544 13.3148 2.5544Z" fill="#3D3D3D"/>
              <path d="M13.3153 6.46094C9.7035 6.46094 6.76953 9.38984 6.76953 13.0067C6.76953 16.6236 9.69844 19.5525 13.3153 19.5525C16.9322 19.5525 19.8611 16.6236 19.8611 13.0067C19.8611 9.38984 16.9322 6.46094 13.3153 6.46094ZM13.3153 17.2508C10.9681 17.2508 9.06612 15.3488 9.06612 13.0017C9.06612 10.6545 10.9681 8.75246 13.3153 8.75246C15.6625 8.75246 17.5645 10.6545 17.5645 13.0017C17.5645 15.3488 15.6625 17.2508 13.3153 17.2508Z" fill="white"/>
              <path d="M20.1195 7.72334C20.9632 7.72334 21.6472 7.03937 21.6472 6.19565C21.6472 5.35194 20.9632 4.66797 20.1195 4.66797C19.2758 4.66797 18.5918 5.35194 18.5918 6.19565C18.5918 7.03937 19.2758 7.72334 20.1195 7.72334Z" fill="white"/>
            </g>
            <defs>
              <clipPath id="clip0_7998_27598">
                <rect width="25.485" height="25.485" fill="white" transform="translate(0.572266 0.257812)"/>
              </clipPath>
            </defs>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-primary-gradient">@healthpass</h3>
      </div>
    </div>

    <!-- Newsletter Card -->
    <div
      class="grid items-stretch content-between p-6 rounded-2xl md:w-1/2 h-56"
      style="
        background:
          url('https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Group_3_1.png?v=**********');
        background-size: cover; /* Ensure the image covers the element */
        background-position: center;
      "
    >
      <div class="heading-block w-full">
        <h2 class="text-3xl font-bold mb-2">Subscribe our newsletter</h2>
        <p class="text-base text-green-7">Stay in the loop with everything you need to know.</p>
      </div>
      <div class="newslettor-block">
        <form class="flex flex-col items-center w-full md:flex-row gap-2 md:w-[90%]">
          <input
            placeholder="Enter your email"
            required=""
            type="text"
            class="rounded-lg border py-3 px-4 border-gray-2 placeholder-gray-5 bg-white w-full text-secondary text-base focus:border-gray-2 focus:outline-none"
            style="box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);"
          >
          <button
            type="submit"
            class="px-4 py-3 button-primary-gradient-outline text-center rounded-full h-12 flex justify-center items-center"
          >
            <span class="text-primary-gradient text-sm font-bold">Subscribe</span>
          </button>
        </form>
      </div>
    </div>
  </div>
</div>
{% comment %}
  {%- form 'customer', id: 'newslettor_subscribes', class: '' -%}
    <input type="hidden" name="contact[tags]" value="newsletter">
    <div class="newsletter-form__field-wrapper">
      <div class="field">
        <input
          id="NewsletterForm--{{ section.id }}"
          type="email"
          name="contact[email]"
          class="transition duration-300 ease-in-out mb-4 h-12 w-full rounded border  text-sm px-4 outline-none focus:border-primary focus-visible:shadow-none"
          value="{{ form.email }}"
          aria-required="true"
          autocorrect="off"
          autocapitalize="off"
          autocomplete="email"
          {% if form.errors %}
            autofocus
            aria-invalid="true"
            aria-describedby="newslettor_subscribes-error"
          {% elsif form.posted_successfully? %}
            aria-describedby="newslettor_subscribes-success"
          {% endif %}
          placeholder="{{ 'newsletter.label' | t }}"
          required
        >
        <button
          type="submit"
          class="mb-6 h-12 w-full cursor-pointer rounded text-white bg-primary text-center text-sm font-medium flex items-center justify-center transition duration-300 ease-in-out"
          name="commit"
          id="Subscribe"
          aria-label="{{ 'newsletter.button_label' | t }}"
        >
          {{ 'Subscribe Now' -}}
        </button>
      </div>
      {%- if form.errors -%}
        <div class=" flex items-center justify-center">
          {{ 'icon-success' }}

          <h3 class="ml-4 text-green-600 font-bold" id="newslettor_subscribes-error">
            {{ form.errors.translated_fields.email | capitalize }}
            {{ form.errors.messages.email }}
          </h3>
        </div>
      {%- endif -%}
    </div>

    {%- if form.posted_successfully? -%}
      <div class=" flex items-center justify-center">
        {{ 'icon-success' }}

        <h3 class="ml-4 text-green-600 font-bold" id="newslettor_subscribes-success" tabindex="-1" autofocus>
          {{ 'newsletter.success' | t }}
        </h3>
      </div>
    {% else %}
      <p class="text-sm font-medium ">Don't worry, we don't spam</p>
    {%- endif -%}
  {%- endform -%}
{% endcomment %}
{% schema %}
{
  "name": "Social Connect",
  "settings": [],
  "presets": [
    {
      "name": "Social Connect"
    }
  ]
}
{% endschema %}
