{% liquid
  assign current_variant = product.selected_or_first_available_variant | default: product.variants.first
%}
<div class="price-selector-block">
  {% form 'product', product, onsubmit: 'onSubmitAddToCartForm(this, event)' %}
    <input type="hidden" name="id" value="{{ current_variant.id }}" data-product-type="{{ product.type }}">
    {% unless product.has_only_default_variant %}
      {% for option in product.options_with_values %}
        <p
          class="text-[11px] leading-4 uppercase mb-1 font-semibold {% if subscription_product == true %}text-white {% else %}text-secondary{% endif %}"
        >
          {{ option.name }}
        </p>
        <div
          class="pricing-group flex flex-col md:flex-row gap-6 md:gap-2 justify-between {% unless subscription_product == true %} md:mb-[52px] {% endunless %}"
        >
          <div
            class="flex w-fit gap-2 p-1.5 {% if subscription_product == true %}bg-[#1B2842] {% else %}bg-[#F5F5F5] {% endif %} rounded-lg"
          >
            {% for value in option.values %}
              {% assign variant = product.variants | where: 'option1', value | first %}
              <div>
                <input
                  onchange="onProductOptionChange(this, event)"
                  product_id="{{ product.id }}"
                  data-product-handle="{{ product.handle }}"
                  type="radio"
                  name="variant_product"
                  data-variant-id="{{ variant.id }}"
                  data-variant-price="{{ variant.price | money_without_trailing_zeros  }}"
                  id="{{ value }}"
                  value="{{ value }}"
                  class="product-option peer hidden"
                  {% if value == option.selected_value %}
                    checked
                  {% endif %}
                >
                <label
                  for="{{ value }}"
                  class="block cursor-pointer select-none text-center px-3.5 py-2.5 text-base {% if subscription_product == true %}text-white peer-checked:text-[#101828] peer-checked:bg-white {% else %}text-[#101828] peer-checked:bg-[#1B2842] peer-checked:text-white {% endif %} rounded-md font-semibold min-w-14 h-11"
                >
                  {{- value -}}
                </label>
              </div>
            {% endfor %}
          </div>
          <div class="price-swapper-block flex flex-col items-center md:items-end">
            <p
              id="displayedPrice"
              class="product-price text-[48px] leading-[54px] {% if subscription_product == true %}text-white {% else %}text-[#101828]{% endif %} !font-semibold"
            >
              <span class="price-text">{{ current_variant.price | money_without_trailing_zeros }}</span>
            </p>
          </div>
        </div>
      {% endfor %}
    {% endunless %}
    <div class="mt-6 md:mt-8">
      <button
        name="add"
        type="submit"
        class="btn-atc button-primary-gradient flex justify-center items-center gap-4 !text-base w-full !py-3"
        role="button"
      >
        {% render 'white-spinner-icon' %}
        <span> {{ button_text }}</span>
      </button>
    </div>
  {% endform %}
</div>
