{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  .tabs-content-wrapper {
    background: linear-gradient(180deg, #F2EEF2 0%, rgba(242, 238, 242, 0.00) 100%);
  }

  details.tab-accordion-block {
    border-radius: 6px;
    border: 1px solid var(--color-primary);
    background: var(--color-primary-light);
  }

  .list-item-block {
    position: absolute;
    z-index: 2;
    background: #ccc;
    width: 100%;
    left: 0;
    top: 47px;
    border: 1px solid var(--color-primary);
    padding: 16px;
    border-radius: 6px;
    background: var(--color-primary-light);
  }
{%- endstyle -%}

<div class="tabs-content-wrapper section-{{ section.id }}-margin py-12 md:py-16 lg:py-20">
  <div class="container px-4 lg:px-0">
    <div class="heading-block max-w-[765px] mb-6 md:mb-12 text-center mx-auto">
      <h1 class="section-heading-40 text-secondary">
        {{ section.settings.heading }}
      </h1>
      <div class="mt-2 md:mt-2 text-base">
        {{ section.settings.paragraph }}
      </div>
    </div>
    <div class="tabs-block rounded-2xl bg-white" style="box-shadow: 0px 0px 44px 0px rgba(0, 0, 0, 0.10);">
      <!-- Accordion for small screens -->
      <div class="block md:hidden px-4 pt-4 pb-6">
        <details
          id="accordion"
          class="group  tab-accordion-block py-2.5 px-3 border border-primary relative"
          open
        >
          <summary class="cursor-pointer flex justify-between items-center">
            <span id="accordionActiveLabel" class="text-primary-gradient text-base">
              {{- section.blocks.first.settings.tab_title -}}
            </span>
            <svg
              class="transform transition-transform duration-200 group-open:rotate-180"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="21"
              viewBox="0 0 20 21"
              fill="none"
            >
              <path d="M5 8.04492L10 13.0449L15 8.04492" stroke="#7E7E7E" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </summary>
          <ul class="list-item-block pt-5 space-y-2">
            {% for tab in section.blocks %}
              <li class="">
                <button
                  class="w-full text-left text-base text-secondary"
                  data-accordion-tab="{{- tab.id -}}"
                  type="button"
                >
                  {{- tab.settings.tab_title -}}
                </button>
              </li>
            {% endfor %}
          </ul>
        </details>
      </div>

      <!-- Tabs for large screens -->
      <div class="border-b border-gray-2 hidden md:block">
        <ul
          data-tabs-toggle="#default-tab-content"
          data-tabs-active-classes="text-[#EB3B72] border-b-2 border-[#EB3B72] bg-primary-light"
          data-tabs-inactive-classes="text-gray-6 border-gray-2"
          class="flex flex-row flex-wrap text-[13px] font-medium text-center justify-between"
          id="default-tab"
          role="tablist"
        >
          {% for tab in section.blocks %}
            <li role="presentation">
              <button
                class="px-4 py-3 border-b-2 {% if forloop.first %}rounded-tl-2xl rounded-tr-2xl md:rounded-tr-none {% elsif forloop.last %}md:rounded-tr-2xl {% endif %}"
                id="{{- tab.id -}}-tab"
                data-tabs-target="#{{- tab.id -}}"
                type="button"
                role="tab"
                aria-controls="{{- tab.id -}}"
                aria-selected="{% if forloop.first %}true{% else %}false{% endif %}"
              >
                {{- tab.settings.tab_title -}}
              </button>
            </li>
          {% endfor %}
        </ul>
      </div>

      <!-- Tab content shared across both modes -->
      <div id="default-tab-content" class="px-4">
        {% for tab in section.blocks %}
          <div
            class="{% if forloop.first %}block{% else %}hidden{% endif %} rounded-lg"
            id="{{ tab.id }}"
            role="tabpanel"
            aria-labelledby="{{ tab.id }}-tab"
          >
            {% liquid
              assign tab_heading = tab.settings.tab_heading
              assign tab_paragraph = tab.settings.tab_paragraph
              assign tab_tags = tab.settings.tab_tags | split: ','

              if tab.settings.shopify_hosted_video != blank
                assign video_src = tab.settings.shopify_hosted_video.sources[0].url
              else
                assign video_src = tab.settings.external_video_link
              endif
            %}
            {% render 'tab-content',
              title: tab_heading,
              paragraph: tab_paragraph,
              tags: tab_tags,
              video_link: video_src,
              tab_settings: tab.settings
            %}
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', () => {
    const accordionBtns = document.querySelectorAll('[data-accordion-tab]');
    const accordionLabel = document.getElementById('accordionActiveLabel');
    const accordionDetails = document.getElementById('accordion');

    accordionBtns.forEach((btn) => {
      btn.addEventListener('click', () => {
        const targetId = btn.getAttribute('data-accordion-tab');

        // Hide all tab content
        document.querySelectorAll('[role="tabpanel"]').forEach((panel) => {
          panel.classList.add('hidden');
        });

        // Show the selected content
        const targetPanel = document.getElementById(targetId);
        if (targetPanel) targetPanel.classList.remove('hidden');

        // Update label
        accordionLabel.textContent = btn.textContent;

        // Update selected class
        accordionBtns.forEach((b) => b.classList.remove('text-primary-gradient'));
        btn.classList.add('text-primary-gradient');

        // Close the accordion
        accordionDetails.removeAttribute('open');
      });
    });

    // Optional: Auto-select the first tab
    const firstTab = accordionBtns[0];
    if (firstTab) firstTab.click();
  });
</script>

{% schema %}
{
  "name": "Tabs",
  "max_blocks": 8,
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "header",
      "content": "General Content"
    },
    {
      "type": "html",
      "id": "heading",
      "label": "Heading",
      "default": "A health screen for everyone"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Subtext",
      "default": "<p>Measure millions of body points, hundreds of biomarkers, and predict dozens of health outcomes in one single health screen.</p>"
    }
  ],
  "blocks": [
    {
      "type": "tab",
      "name": "Tab",
      "settings": [
        {
          "type": "text",
          "id": "tab_title",
          "label": "Tab Title",
          "default": "Tab Title"
        },
        {
          "type": "text",
          "id": "tab_heading",
          "label": "Tab Heading",
          "default": "Tab Heading"
        },
        {
          "type": "textarea",
          "id": "tab_paragraph",
          "label": "Tab Content",
          "default": "Enter the description for this tab here."
        },
        {
          "type": "text",
          "id": "tab_tags",
          "label": "Tags (comma-separated)",
          "default": "Tag1, Tag2, Tag3"
        },
        {
          "type": "header",
          "content": "Button block"
        },
        {
          "type": "text",
          "id": "btn_text",
          "label": "Button text",
          "default": "Read More",
          "info": "Leave empty to disable"
        },
        {
          "type": "url",
          "id": "btn_url",
          "label": "Button URL"
        },
        {
          "type": "select",
          "id": "btn_target",
          "label": "Open file",
          "default": "_self",
          "options": [
            {
              "value": "_blank",
              "label": "Open in a New Tab"
            },
            {
              "value": "_self",
              "label": "Open in Same Tab"
            }
          ]
        },
        {
          "type": "select",
          "id": "btn_variant",
          "label": "Select button variant",
          "default": "solid_primary",
          "options": [
            {
              "value": "solid_primary",
              "label": "Primary solid button"
            },
            {
              "value": "outline_primary",
              "label": "Primary outline button"
            }
          ]
        },
        {
          "type": "header",
          "content": "Video block",
          "info": "[Note:] Use only one of the options. When both Shopify hosted video and Video link are added, the Shopify-hosted option will be used."
        },
        {
          "type": "video",
          "id": "shopify_hosted_video",
          "label": "Shopify hosted video"
        },
        {
          "type": "url",
          "id": "external_video_link",
          "label": "External Video Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Tabs",
      "blocks": [
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        }
      ]
    }
  ]
}
{% endschema %}
