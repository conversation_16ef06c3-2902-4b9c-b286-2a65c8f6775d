{%- unless settings.type_body_font.system? -%}
  <link href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
{%- endunless -%}

{%- liquid
  assign type_body_font_medium = settings.type_body_font_medium | font_modify: 'weight', 'medium'
  assign type_body_font_semibold = settings.type_body_font_medium | font_modify: 'weight', 'semibold'
  assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
%}

{% style %}
  {{- settings.type_body_font | font_face: font_display: 'swap' -}}
  {{- settings.type_body_font_medium | font_face: font_display: 'swap' -}}
  {{- settings.type_body_font_semibold | font_face: font_display: 'swap' -}}
  {{- settings.body_font_bold | font_face: font_display: 'swap' -}}
  {{- type_body_font_medium | font_face: font_display: 'swap' -}}
  {{- type_body_font_semibold | font_face: font_display: 'swap' -}}
  {{- body_font_bold | font_face: font_display: 'swap' -}}
  :root {
    --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
    --font-body-style: {{ settings.type_body_font.style }};
    --font-body-weight: {{ settings.type_body_font.weight }};
    --font-body-weight-bold: {{ settings.type_body_font.weight | plus: 300 | at_most: 1000 }};
    --color-primary: #D22725;
    --color-primary-light: #FDEBEE;
    --color-blush-pink: #FFE5F5;
    --color-primary-muted-seafoam: #75908E;
    --color-primary-heather-blush: #9A7798;
    --color-secondary: #161618;
    --color-white: #ffffff;
    --color-black: #000000;
    --gradient-primary: linear-gradient(270deg, #C8277D 0%, var(--color-primary) 100%);
    --color-gray: var(--color-secondary);
    --color-gray-1: #F4F4F4;
    --color-gray-2: #DCDCDC;
    --color-gray-3: #D0D0D0;
    --color-gray-4: #B1B1B1 ;
    --color-gray-5: #959595;
    --color-gray-6: #7E7E7E;
    --color-gray-7: #5A5A5A;
    --color-gray-8: #3D3D3D;
    --color-gray-9: #242424;
    --color-gray-10: var(--color-secondary);
  }
{% endstyle %}

{{ 'style.css' | asset_url | stylesheet_tag }}
{{ 'shopify-cookie-banner.css' | asset_url | stylesheet_tag }}
{{ 'base.css' | asset_url | stylesheet_tag }}
{% if template == 'index'
  or template.name == 'product'
  or template == 'page.partners'
  or template == 'page.body-scans'
%}
  {{ 'owl.carousel.min.css' | asset_url | stylesheet_tag }}
  {{ 'owl.theme.default.css' | asset_url | stylesheet_tag }}
{% endif %}

<script src="{{ 'global.js' | asset_url }}" defer></script>
<script src="{{ 'header.js' | asset_url }}" defer></script>
<script src="{{ 'constants.js' | asset_url }}" defer></script>
<script src="{{ 'styku-services.js' | asset_url }}" defer></script>
<script src="{{ 'shopify-service.js' | asset_url }}" defer></script>
<script src="{{ 'product.js' | asset_url }}" defer></script>
<script src="{{ 'cart.js' | asset_url }}" defer></script>
<script src="{{ 'video-modal-handler.js' | asset_url }}" defer></script>
<script src="{{ 'toggle-button.js' | asset_url }}" defer></script>

{% if template == 'index' or template == 'page.body-scans' or template == 'page.locations' %}
  <script src="{{ 'locations-services.js' | asset_url }}" defer></script>
  <script src="{{ 'location.js' | asset_url }}" defer></script>
{% endif %}
