<div class="benefits-block">
  <p class="text-base text-secondary mb-4 font-bold">{{ title }}</p>
  <div class="grid grid-col-1 md:grid-cols-3 gap-4">
    {%- for block in benefits_blocks -%}
      <div class="card-block p-4 grid gap-3 rounded-lg bg-[#F3F3F3] text-center">
        <div class="icon-block flex justify-center">
          {%- if block.settings.icon != blank -%}
            {{ block.settings.icon }}
          {% else %}
            {{- 'icon-payment-billing.svg' | inline_asset_content -}}
          {% endif %}
        </div>
        <p class="text-sm text-secondary">{{ block.settings.title }}</p>
      </div>
    {%- endfor -%}
  </div>
</div>
