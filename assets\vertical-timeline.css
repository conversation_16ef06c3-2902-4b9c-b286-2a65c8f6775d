.time-line-block {
  max-width: 1054px;
  margin: 64px auto auto;
}
.timeline ul {
  padding: 0;
  position: relative;
}
.timeline .default-line {
  content: '';
  position: absolute;
  left: 219px;
  width: 2px;
  background: var(--color-gray-1);
}
.timeline .draw-line {
  width: 2px;
  height: 0;
  position: absolute;
  left: 219px;
  background: var(--color-primary-heather-blush);
}
.timeline ul li {
  list-style-type: none;
  position: relative;
  margin: 0 auto;
  height: auto;
  background: transparent;
}
.timeline ul li:last-child {
  height: auto;
}
.timeline ul li.in-view {
  transition: 0.125s ease-in-out, background-color 0.2s ease-out, color 0.1s ease-out, border 0.1s ease-out;
}
.timeline ul li.in-view::before {
  content: '';
  position: absolute;
  left: 13rem;
  top: 0;
  width: 24px;
  height: 24px;
  background: var(--color-primary-heather-blush);
  border-radius: 50%;
  transition: 0.125s ease-in-out, background-color 0.2s ease-out, color 0.1s ease-out, border 0.1s ease-out;
}
.timeline ul li::before {
  content: '';
  position: absolute;
  left: 13rem;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-gray-1);
  transition: all 0.4s ease-in-out;
}

.overlap-icon-top-right {
  width: 244px;
  height: 540px;
  position: absolute;
  right: -178px;
  rotate: 13deg;
  top: 52px;
  z-index: 1;
  border-radius: 0px 0px 227.354px 227.354px;
  opacity: 0.4;
  background: #c9cdda;
}

@media only screen and (max-width: 600px) {
  .timeline .default-line {
    left: 110px;
  }
  .timeline ul li.in-view::before {
    left: 100px;
  }
  .timeline ul li::before {
    left: 100px;
  }
  .timeline .draw-line {
    left: 110px;
  }
  .time-line-block {
    margin: 32px auto auto;
  }
  .timeline-item:last-child .pb-20 {
    padding-bottom: 32px !important;
  }
}
