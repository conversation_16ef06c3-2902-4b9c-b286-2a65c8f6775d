document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('secureAccount');
  const firstName = document.getElementById('firstName');
  const lastName = document.getElementById('lastName');
  const password = document.getElementById('password');
  const email = document.getElementById('email');
  const policyCheckboxAccepted = document.getElementById('policyCheckboxAccepted');
  const errorMessageGroup = document.querySelector('.error-message-group');
  const minimumCharactersPasswordMessage = errorMessageGroup.querySelector('.minimum-characters-message');
  const upperLowerCaseMessage = errorMessageGroup.querySelector('.upper-lowercase-message');
  const numberPasswordMessage = errorMessageGroup.querySelector('.number-message');
  const specialCharactersMessage = errorMessageGroup.querySelector('.special-characters-message');
  const confirmPassword = document.getElementById('confirmPassword');
  const validConfirmPasswordMessage = document.querySelector('.valid-password');
  const invalidConfirmPasswordMessage = document.querySelector('.invalid-password');
  const submitButton = document.getElementById('submitButton');
  const spinnerIcon = document.getElementById('spinnerIcon');
  const newProfileTitle = document.getElementById('newProfileTitle');
  const multipleScansExistingProfileTitle = document.getElementById('multipleScansExistingProfileTitle');
  const singleScanExistingProfileTitle = document.getElementById('singleScanExistingProfileTitle');
  const exceptionMessage = window.errorMessage;
  let profile = {};

  const emailFromHistory = readFromHistoryState('email');
  // Reload browser on popstate
  reloadOnPopState();

  // Check if the item exists in sessionStorage
  profile = getSessionStorageItem(SESSION_KEY.PROFILE);

  if (!emailFromHistory) {
    window.location.href = '/';
  } else {
    // Update heading base on the profile scans count
    if (profile.scansCount != null && profile.scansCount > 1) {
      newProfileTitle.classList.add('hidden');
      multipleScansExistingProfileTitle.classList.remove('hidden');
      multipleScansExistingProfileTitle.querySelector('.scans-count').textContent = profile.scansCount;
    } else if (profile.scansCount != null && profile.scansCount <= 1) {
      newProfileTitle.classList.add('hidden');
      singleScanExistingProfileTitle.classList.remove('hidden');
      singleScanExistingProfileTitle.querySelector('.scans-count').textContent = profile.scansCount;
    }

    // Automatically populate the first name and last name fields if the profile has data
    firstName.value = profile.firstName ?? '';
    lastName.value = profile.lastName ?? '';
    email.value = profile.email ?? '';

    // Event listeners for input changes
    firstName.addEventListener('input', enableSubmit);
    lastName.addEventListener('input', enableSubmit);

    // Event listener for password input
    password.addEventListener('input', function () {
      enableSubmit();

      const passwordValue = password.value;
      // Check if password length is at least 8 characters
      if (isValidLength(passwordValue, 8)) {
        updatePasswordValidationMessage(minimumCharactersPasswordMessage, true);
        updatePasswordValidationIcon(minimumCharactersPasswordMessage, true);
      } else {
        updatePasswordValidationMessage(minimumCharactersPasswordMessage, false);
        updatePasswordValidationIcon(minimumCharactersPasswordMessage, false);
      }

      if (checkUpperAndLowerCase(passwordValue)) {
        updatePasswordValidationMessage(upperLowerCaseMessage, true);
        updatePasswordValidationIcon(upperLowerCaseMessage, true);
      } else {
        updatePasswordValidationMessage(upperLowerCaseMessage, false);
        updatePasswordValidationIcon(upperLowerCaseMessage, false);
      }

      // Check if password contains at least one digit
      if (containsDigit(passwordValue)) {
        updatePasswordValidationMessage(numberPasswordMessage, true);
        updatePasswordValidationIcon(numberPasswordMessage, true);
      } else {
        updatePasswordValidationMessage(numberPasswordMessage, false);
        updatePasswordValidationIcon(numberPasswordMessage, false);
      }

      // Checks the special characters from the set [@,$,!,%,*,#,?,&]
      if (containsSpecialChar(passwordValue)) {
        updatePasswordValidationMessage(specialCharactersMessage, true);
        updatePasswordValidationIcon(specialCharactersMessage, true);
      } else {
        updatePasswordValidationMessage(specialCharactersMessage, false);
        updatePasswordValidationIcon(specialCharactersMessage, false);
      }

      // Check if confirm password input has value, then validate passwords
      if (confirmPassword.value) {
        validatePasswords();
      }

      hideConfirmPasswordValidationMessage();
    });

    // Event listener for confirm password input
    confirmPassword.addEventListener('input', function () {
      enableSubmit();
      validatePasswords();
      hideConfirmPasswordValidationMessage();
    });

    policyCheckboxAccepted.addEventListener('change', function () {
      enableSubmit();
    });

    // Function to hide confirm password validation messages if both passwords are empty
    function hideConfirmPasswordValidationMessage() {
      if (!password.value && !confirmPassword.value) {
        validConfirmPasswordMessage.classList.add('hidden');
        invalidConfirmPasswordMessage.classList.add('hidden');
      }
    }

    // Function to validate passwords
    function validatePasswords() {
      if (isPasswordsMatch(password, confirmPassword)) {
        // Passwords match
        updatePasswordValidationIcon(validConfirmPasswordMessage, true);
        updatePasswordValidationMessage(validConfirmPasswordMessage, true);
        validConfirmPasswordMessage.classList.remove('hidden');
        invalidConfirmPasswordMessage.classList.add('hidden');
      } else {
        // Passwords do not match
        updatePasswordValidationIcon(invalidConfirmPasswordMessage, false);
        updatePasswordValidationMessage(invalidConfirmPasswordMessage, false);
        validConfirmPasswordMessage.classList.add('hidden');
        invalidConfirmPasswordMessage.classList.remove('hidden');
      }
    }

    // Function to enable/disable submit button based on form validity
    function enableSubmit() {
      submitButton.disabled = !(
        isInputValid(firstName) &&
        isInputValid(lastName) &&
        isPasswordsMatch(password, confirmPassword) &&
        policyCheckboxAccepted.checked
      );
    }

    // Function to check if input field is not empty
    function isInputValid(input) {
      return !!input.value;
    }

    // Function to check if password is valid
    function isPasswordValid(input) {
      // Should be 8 character long and contain one digit
      return isInputValid(input) && isValidLength(input.value, 8) && containsDigit(input.value);
    }

    // Function to check if passwords match
    function isPasswordsMatch(passwordEle, confirmPasswordEle) {
      return (
        isPasswordValid(passwordEle) &&
        isPasswordValid(confirmPasswordEle) &&
        passwordEle.value === confirmPasswordEle.value
      );
    }

    // Add an event listener to the form submission event
    form.addEventListener('submit', async function (event) {
      event.preventDefault();

      submitButton.disabled = true;
      spinnerIcon.classList.remove('hidden');

      // Extract form data and construct a request body object
      const formData = new FormData(form);
      const requestBody = {};
      formData.forEach((value, key) => {
        requestBody[key] = value;
      });

      requestBody.email = profile.email;

      try {
        // Destructure password and confirmPassword from the request body, leaving the rest as profile data
        const { password, confirmPassword, ...profile } = requestBody;

        const secureAccountPayload = API_PAYLOADS.SECURE_ACCOUNT;
        secureAccountPayload.body = JSON.stringify(requestBody);

        const response = await fetchData(secureAccountPayload);

        if (response.ok) {
          const responseData = await response.json();

          switch (responseData.status) {
            case SUCCESS_CODE.SUCCESS:
              // Login into styku
              await profileLogin(requestBody.email, password);
              // Login into shopify
              await shopifyCustomerLogin(profile.email, password);

              sessionStorage.removeItem(SESSION_KEY.PROFILE);
              //  Deletes email item from the browser's history state and redirect on the account page
              deleteHistoryStateItem('email');
              handleQueryParamRedirect(QUERY_PARAMS_KEY.CHECKOUT_URL, PAGE_URLS.ACCOUNT);
              break;
            default:
              showToastMessage(exceptionMessage.somethingWentWrong);
              break;
          }
        } else {
          showToastMessage(exceptionMessage.somethingWentWrong);
        }
      } catch (error) {
        console.error('Error while securing profile:', error);
        showToastMessage(exceptionMessage.somethingWentWrong);
      } finally {
        submitButton.disabled = false;
        spinnerIcon.classList.add('hidden');
      }
    });
  }
});
