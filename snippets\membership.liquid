{% liquid
  assign is_subscription_active = is_subscription_active
  assign is_subscription_cancelled = is_subscription_cancelled
  assign is_subscription_expired = is_subscription_expired
  assign is_subscription_failed = is_subscription_failed
  assign scans_count = scans_count

  if customer.metafields.appstle_subscription.subscriptions.value and customer.metafields.appstle_subscription.subscriptions.value.size > 0
    assign subscription_details = customer.metafields.appstle.subscription_details.value
    assign subcription_created_at = subscription_details.created_at | date: '%b %d %Y'
    assign next_billing_date = subscription_details.next_billing_date | date: '%b %d %Y'
    assign status = subscription_details.status | upcase
  endif
%}

<style>
  .shape-left-bottom {
    width: 119.644px;
    height: 265px;
    transform: rotate(-90deg);
    position: absolute;
    bottom: -68.644px;
    border-radius: 0px 0px 112px 112px;
    opacity: 0.4;
    background: #c2d3d2;
    left: 48px;
    bottom: -132px;
    z-index: 0;
  }
  .shape-right-center {
    position: absolute;
    right: -28px;
    z-index: -1;
    top: -66px;
  }

  @media screen and (max-width: 750px) {
    .image-block {
      right: -28px;
      z-index: 1;
    }
    .shape-right-center {
      top: -28px;
    }
  }
</style>

<div class="membership-information">
  <div class="see-block-test-container flex flex-col md:flex-row justify-between items-center border border-gray-2 relative overflow-hidden rounded-2xl mb-6 md:mb-0">
    <div class="see-blood-test-block max-w-md p-6 md:p-9 relative z-[1] shrink-0">
      {% if is_subscription_active or is_subscription_cancelled %}
        <div class="content-block button-primary-light py-2 !px-3 inline-flex mb-4">
          <span class="text-primary-gradient text-sm">{{- 'general.customer.membership' | t -}}</span>
        </div>
        <div class="flex flex-col gap-1 mb-6">
          <p class="text-base text-secondary font-bold">
            {{- 'general.customer.member_since' | t -}}
            <span class="ms-2 rounded-full px-2 py-1 text-xs {% if status == 'ACTIVE' %}text-green-800 bg-green-100{% else %}text-red-800 bg-red-100{% endif %}">
              {{- status | upcase -}}
            </span>
          </p>
          <p class="text-base text-secondary">{{- subcription_created_at -}}</p>
          {% if is_subscription_active %}
            <p class="text-base text-gray-8">
              {{- 'general.customer.subscription_will_renew_on' | t: date: next_billing_date -}}
            </p>
          {% endif %}
          {% if is_subscription_cancelled %}
            <p class="text-base text-gray-8">
              {{- 'general.customer.subscription_valid_until' | t: date: next_billing_date -}}
            </p>
          {% endif %}
        </div>
        <a
          href="/apps/subscriptions#/subscriptions/{{ customer.metafields.appstle_subscription.subscriptions.value[0].id }}/detail"
          class="button-primary-gradient inline-flex justify-center items-center gap-4 !px-8 !text-base !py-3"
        >
          {{ 'general.customer.manage_subscription' | t }}
        </a>
      {% else %}
        <h2 class="text-secondary font-bold text-2xl md:text-[28px] md:leading-none mb-1">
          {{ 'general.customer.no_scans_available' | t | remove: 'scans' }}
        </h2>
        <h2 class="text-primary-gradient font-bold text-2xl md:text-[28px] md:leading-none mb-2">
          {{ 'general.customer.healthpass_subscription' | t }}
        </h2>
        <p class="text-base text-gray-8 mb-3">
          {{ 'general.customer.healthpass_subscription_message' | t }}
        </p>
        <a
          href="/pages/pricing"
          class="button-primary-gradient inline-flex justify-center items-center gap-4 !px-8 !text-base !py-3"
        >
          {{ 'general.customer.get_healthpass' | t }}
        </a>
      {% endif %}
    </div>
    <div class="image-block relative">
      <div class="shape-right-center absolute right-0 top-0">
        <div class="shape-right-center">
          <svg width="158" height="333" viewBox="0 0 158 333" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path opacity="0.4" d="M157.209 0C157.806 92.7597 158.641 289.215 157.209 332.958C-49.7911 332.958 -54.9987 0 157.209 0Z" fill="#F84642"/>
          </svg>
        </div>
      </div>
      <img
        src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/styku-mechine-image.png?v=**********"
        loading="lazy"
        width="356"
        height="auto"
      >
    </div>
    <div class="shape-left-bottom absolute left-0 h-full w-full z-[1]"></div>
  </div>
</div>
