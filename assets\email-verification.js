document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('emailVerificationCode');
  const resendVerificationCode = document.getElementById('resendVerificationCode');
  const verificationCodeInput = document.getElementById('verificationCode');
  const submitButton = document.getElementById('submitButton');
  const spinnerIcon = document.getElementById('spinnerIcon');
  const title = document.getElementById('title');
  const description = document.getElementById('description');
  const exceptionMessage = window.errorMessage;

  const forgetPasswordStringFromHistory = readFromHistoryState('forgetPasswordString');
  const emailFromHistory = readFromHistoryState('email');

  // Update the title and description if the state exists
  if (forgetPasswordStringFromHistory) {
    title.textContent = forgetPasswordStringFromHistory.title;
    description.textContent = forgetPasswordStringFromHistory.description;
  }

  // Reload browser on popstate
  reloadOnPopState();

  if (!emailFromHistory) {
    window.location.href = '/';
  } else {
    verificationCodeInput.addEventListener('input', function (event) {
      // Remove any non-numeric characters
      let numericInput = event.target.value.replace(/[^0-9]/g, '');
      // Restrict input to maximum of six characters
      if (numericInput.length > 6) {
        numericInput = numericInput.slice(0, 6);
      }
      event.target.value = numericInput;
      submitButton.disabled = numericInput.length !== 6;
    });

    form.addEventListener('submit', async function (event) {
      event.preventDefault();

      submitButton.disabled = true;
      spinnerIcon.classList.remove('hidden');

      const requestBody = {
        email: emailFromHistory,
        code: verificationCodeInput.value,
      };

      try {
        const profile = { email: emailFromHistory };
        const emailVerificationPayload = API_PAYLOADS.EMAIL_VERIFICATION_CODE;
        emailVerificationPayload.body = JSON.stringify(requestBody);

        const response = await fetchData(emailVerificationPayload);

        if (response.ok) {
          const responseData = await response.json();

          switch (responseData.status) {
            case SUCCESS_CODE.SUCCESS:
              setSessionStorageItem(SESSION_KEY.PROFILE, responseData.data);
              if (forgetPasswordStringFromHistory) {
                updateHistoryState(profile, PAGE_URLS.RESET_PASSWORD);
              } else {
                updateHistoryState(profile, PAGE_URLS.SHOPIFY_REGISTER);
              }
              break;
            case ERROR_CODES.INVALID_VERIFICATION_CODE:
              showToastMessage(exceptionMessage.invalidVerificationCode);
              break;
            case ERROR_CODES.INVALID_EMAIL:
              showToastMessage(exceptionMessage.invalidEmail);
              break;
            default:
              showToastMessage(exceptionMessage.somethingWentWrong);
              break;
          }
        } else {
          showToastMessage(exceptionMessage.somethingWentWrong);
        }
      } catch (error) {
        console.error('Error while verifying verification code:', error);
        showToastMessage(exceptionMessage.somethingWentWrong);
      } finally {
        submitButton.disabled = false;
        spinnerIcon.classList.add('hidden');
      }
    });

    // Function to resend a verification code
    resendVerificationCode.addEventListener('click', async function (event) {
      await sendVerificationCode(event, submitButton, emailFromHistory);
    });
  }
});
