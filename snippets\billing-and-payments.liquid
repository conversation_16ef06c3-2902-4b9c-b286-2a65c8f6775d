<div class="billing-and-payments-informations space-y-6">
  <div class="rounded-2xl border border-gray-2 p-4 md:p-6">
    <div class="header-block mb-6">
      <h3 class="heading-level-3">{{ 'customer.orders.title' | t }}</h3>
    </div>
    {%- if customer.orders.size > 0 -%}
      <div class="customer-orders-block relative border border-gray-2 overflow-x-auto rounded-lg">
        <table role="table" class="w-full text-sm text-left text-gray-8">
          <thead role="rowgroup" class="text-sm text-secondary uppercase bg-gray-1">
            <tr role="row">
              <th id="ColumnOrder" scope="col" class="py-4 px-2" role="columnheader">
                {{ 'customer.orders.order_number' | t }}
              </th>
              <th id="ColumnDate" scope="col" class="py-4 px-2" role="columnheader">
                {{ 'customer.orders.date' | t }}
              </th>
              <th id="ColumnItemCount" scope="col" class="py-4 px-2" role="columnheader">
                {{ 'customer.order.quantity' | t }}
              </th>
              <th id="ColumnPayment" scope="col" class="py-4 px-2" role="columnheader">
                {{ 'customer.orders.payment_status' | t }}
              </th>
              <th id="ColumnFulfillment" scope="col" class="py-4 px-2" role="columnheader">
                {{ 'customer.orders.fulfillment_status' | t }}
              </th>
              <th id="ColumnTotal" scope="col" class="py-4 px-2" role="columnheader">
                {{ 'customer.orders.total' | t }}
              </th>
            </tr>
          </thead>
          <tbody role="rowgroup">
            {%- for order in customer.orders -%}
              <tr role="row" class="bg-white border-b last:border-none hover:bg-[#fafafaea]">
                <th
                  id="RowOrder"
                  role="cell"
                  headers="ColumnOrder"
                  class="py-4 px-2 font-medium whitespace-nowrap"
                  data-label="{{ 'customer.orders.order_number' | t }}"
                >
                  <a
                    href="{{ order.customer_url }}"
                    class="p-2 bg-gray-1 hover:bg-gray-2 rounded"
                    aria-label="{{ 'customer.orders.order_number_link' | t: number: order.name }}"
                  >
                    {{ order.name }}
                  </a>
                </th>
                <td
                  headers="RowOrder ColumnDate"
                  class="py-4 px-2"
                  role="cell"
                  data-label="{{ 'customer.orders.date' | t }}"
                >
                  {{ order.created_at | date: '%b %d %Y' }}
                </td>
                <td
                  headers="RowOrder ColumnItemCount"
                  class="py-4 px-2"
                  role="cell"
                  data-label="{{ 'customer.order.quantity' | t }}"
                >
                  {{ order.item_count }}
                </td>
                <td
                  headers="RowOrder ColumnPayment"
                  role="cell"
                  class="py-4 px-2"
                  data-label="{{ 'customer.orders.payment_status' | t }}"
                >
                  {% liquid
                    assign styles = ''
                    case order.financial_status_label
                      when 'Paid'
                        assign styles = 'bg-green-100 text-green-800'
                      when 'Refunded', 'Partially refunded'
                        assign styles = 'bg-red-100 text-red-800'
                      else
                        assign styles = 'bg-gray-100 text-gray-800'
                    endcase
                  %}
                  <span class="status text-xs font-medium px-2 py-0.5 rounded-md select-none {{ styles }}">
                    {{ order.financial_status_label }}
                  </span>
                </td>
                <td
                  headers="RowOrder ColumnFulfillment"
                  role="cell"
                  class="py-4 px-2"
                  data-label="{{ 'customer.orders.fulfillment_status' | t }}"
                >
                  {% liquid
                    assign styles = ''
                    case order.fulfillment_status_label
                      when 'Fulfilled'
                        assign styles = 'bg-green-100 text-green-800'
                      when 'Unfulfilled'
                        assign styles = 'bg-yellow-100 text-yellow-800'
                      else
                        assign styles = 'bg-gray-100 text-gray-800'
                    endcase
                  %}
                  <span class="status text-xs font-medium px-2 py-0.5 rounded-md select-none {{ styles }}">
                    {{ order.fulfillment_status_label -}}
                  </span>
                </td>
                <td
                  headers="RowOrder ColumnTotal"
                  class="py-4 px-2"
                  role="cell"
                  data-label="{{ 'customer.orders.total' | t }}"
                >
                  {{ order.total_net_amount | money_with_currency }}
                </td>
              </tr>
            {%- endfor -%}
          </tbody>
        </table>
      </div>
    {%- else -%}
      <div class="empty-block">
        <p class="text-base text-secondary">{{ 'customer.orders.none' | t }}</p>
      </div>
    {%- endif -%}
  </div>
</div>
