<style>
  #password {
    display: none;
  }

  #password:target {
    display: block;
  }

  #password:target ~ #login {
    display: none;
  }
</style>

<div class="container px-4 lg:px-0">
  <div class="main-wrapper mx-auto max-w-[600px] mb-7 mt-9 md:mb-0 md:mt-12">
    <div class="logo-block flex justify-center mb-5">{{- 'styku-logo.svg' | inline_asset_content -}}</div>
    <div id="password">
      <div class="header-block mb-11 text-center">
        <h2 class="text-[28px] font-bold text-secondary leading-9 mb-2.5">
          {{ 'sections.login.password_screen.title' | t }}
        </h2>
        <p class="paragraph-text">{{ 'sections.login.password_screen.description' | t }}</p>
      </div>
      <form id="passwordForm" class="form-block">
        <div class="input-block mb-3.5">
          {% assign password_placeholder = 'input.password.placeholder' | t %}
          <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.password.label' | t }}</label>
          {% render 'toggle-password-visibility', id: 'password-input', placeholder: password_placeholder %}
        </div>
        <div class="link-block leading-[0px]">
          <button type="button" role="button" id="forgetPassword" class="text-sm text-primary-gradient">
            {{- 'general.customer.forget_password' | t -}}
          </button>
        </div>
        <div class="button-block mt-6">
          <button
            id="passwordSubmitButton"
            class="button-primary-gradient flex justify-center items-center gap-4 !text-base w-full !py-3"
            disabled
            role="button"
          >
            {% render 'white-spinner-icon', id: 'passwordSubmitButtonSpinnerIcon' %}
            <span>{{ 'general.customer.next' | t }}</span>
          </button>
        </div>
      </form>
    </div>
    <div id="login" tabindex="-1">
      <div id="loginMessage" class="text-content-block mb-8">
        <h3 class="heading-level-3 mb-3 text-center">{{ 'sections.login.email_screen.title' | t }}</h3>
        <p class="paragraph-text text-center">
          {{ 'sections.login.email_screen.description' | t }}
        </p>
      </div>
      <div id="loginWelcomeMessage" class="text-content-block mb-8 hidden">
        <h3 class="heading-level-3 mb-3 text-center">{{ 'sections.login.email_screen_while_checkout.title' | t }}</h3>
        <div class="space-y-4">
          <p class="paragraph-text">
            {{ 'sections.login.email_screen_while_checkout.description' | t }}
          </p>
          <p class="paragraph-text">
            {{ 'sections.login.email_screen_while_checkout.returning_styku_users' | t }}
          </p>
          <p class="paragraph-text">
            {{ 'sections.login.email_screen_while_checkout.new_styku_user' | t }}
          </p>
        </div>
      </div>
      <form id="verifyProfile" class="form-block space-y-6">
        <div class="input-block">
          <div class="mb-6">
            <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.email.label' | t }}</label>
            <input
              type="email"
              name="email"
              id="email"
              class="bg-white border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
              placeholder="{{ 'input.email.placeholder' | t }}"
            >
          </div>
        </div>
        <div id="benefitsMessageBox" class="message-box hidden">
          {% render 'account-creation-benefits', benefits_blocks: section.blocks, title: section.settings.title %}
        </div>
        <div class="button-block">
          <button
            id="verifyProfileSubmit"
            class="button-primary-gradient flex justify-center items-center gap-4 !text-base w-full !py-3"
            disabled
            role="button"
          >
            {% render 'white-spinner-icon', id: 'verifyProfileSubmitSpinnerIcon' %}
            <span>{{ 'general.customer.continue' | t }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% render 'shopify-customer-login-form' %}

<script src="{{ 'verify-profile.js' | asset_url }}" defer></script>
<script src="{{ 'toggle-password-visibility.js' | asset_url }}" defer></script>
{% schema %}
{
  "name": "Login",
  "class": "index-section",
  "max_blocks": 3,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Benefits of account creation and verification"
    }
  ],
  "blocks": [
    {
      "type": "account_creation_benefits",
      "name": "Account Creation Benefits",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Manage billing & payments"
        },
        {
          "type": "html",
          "id": "icon",
          "label": "Add icon SVG code"
        }
      ]
    }
  ]
}
{% endschema %}
