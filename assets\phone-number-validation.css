input.error {
  border: 1px solid #ff5959;
}

.hide {
  display: none;
}

.iti {
  position: relative;
  display: inline-block;
  width: 100%;
}

/* Styling for the Custom scrollbar */
.iti__country-list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px var(--color-white);
  border-radius: 10px;
}

.iti__country-list::-webkit-scrollbar {
  width: 4px;
  background-color: var(--color-white);
}

.iti__country-list::-webkit-scrollbar-thumb {
  border-radius: 6px;
  -webkit-box-shadow: inset 0 0 6px #c1c1c1;
  height: 100px;
  background-color: var(--color-white);
}

.iti__search-input {
  width: 100%;
  border-width: 0;
  border-radius: 8px;
  height: 44px;
  padding: 8px 16px;
  font-size: 16px;
  font-family: 'Poppins';
  outline: none;
}

.iti__country {
  display: flex;
  align-items: center;
  padding: 4px 16px;
  font-size: 14px !important;
  outline: none;
}

.iti--inline-dropdown .iti__dropdown-content {
  position: absolute;
  z-index: 9999;
  margin-top: 4px;
  margin-left: 0px;
  border: 1px solid #ccc;
  box-shadow: none;
  border-radius: 8px;
  padding: 0 0 8px 0;
}

.iti__dropdown-content {
  border-radius: 8px;
  background-color: white;
}
