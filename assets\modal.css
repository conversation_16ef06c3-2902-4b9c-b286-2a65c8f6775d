@media only screen and (max-width: 1024px) {
  .modal-overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    background-color: rgba(19, 19, 19, 0.8);
    opacity: 0;
    visibility: hidden;
  }

  .modal-overlay.active {
    opacity: 1;
    visibility: visible;
    z-index: 10;
  }

  .modal .modal-content {
    opacity: 0;
  }

  .modal.active {
    visibility: visible;
    opacity: 1;
  }

  .modal.active .modal-content {
    opacity: 1;
  }

  .modal.active .close-modal {
    opacity: 1;
  }
}
