{% liquid
  assign current_variant = product.selected_or_first_available_variant
%}

<div class="form-wrapper">
  {% form 'product', product, onsubmit: 'onSubmitAddToCartForm(this, event)' %}
    <input
      type="hidden"
      name="id"
      value="{{ current_variant.id }}"
      data-product-type="{{ product.type }}"
    >
    {% unless current_variant.selling_plan_allocations.size == 0 %}
      {% if customer.tags contains 'appstle_subscription_active_customer' %}
        <p class="text-xs text-gray-7 mb-4">
          {{ 'general.customer.already_have_active_subscription' | t | append: '<br>' }}
          <a
            class="underline text-primary-gradient font-bold text-sx"
            href="/apps/subscriptions#/subscriptions/{{ customer.metafields.appstle_subscription.subscriptions.value[0].id }}/detail"
          >
            {{- 'general.customer.view_membership' | t -}}
          </a>
        </p>
      {% endif %}
      {% render 'selling-plans-integration', product: product %}
    {% endunless %}
    {% liquid
      assign is_added_selling_plan_to_cart = false
      for line_item in cart.items
        if line_item.selling_plan_allocation
          assign is_added_selling_plan_to_cart = true
        endif
      endfor
    %}
    <div class="flex">
      <button
        class="btn-atc button-primary-light w-full py-2 flex justify-center items-center gap-4"
        type="submit"
        name="add"
        data-text-add-to-cart="{{ 'product.add_to_cart' | t }}"
        {% unless current_variant.selling_plan_allocations.size == 0 %}
          data-selling-plan-product="true"
        {% endunless %}

        {% if current_variant.selling_plan_allocations.size > 0
          and is_added_selling_plan_to_cart
          or is_health_pass_customer
          or customer.tags contains 'appstle_subscription_active_customer'
        %}
          data-selling-plan-in-cart="true"
          disabled
        {% endif %}
      >
        {% render 'white-spinner-icon', text_color: 'text-[#C8277D]' %}
        {% if current_variant.available %}
          <span class="buttun-text text-primary-gradient">
            {%- if button_text %}{{- button_text -}}{% else %}{{- 'product.add_to_cart' | t -}}{% endif %}
          </span>
        {% else %}
          <span class="opacity-20">{{ 'product.sold_out' | t }}</span>
        {% endif %}
      </button>
    </div>
  {% endform %}
</div>
