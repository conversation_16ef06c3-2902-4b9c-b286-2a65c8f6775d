{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  .richtext-custom .content strong {
    color: var(--color-secondary);
  }

  .richtext-custom .content {
    color: var(--color-gray-5);
  }
{%- endstyle -%}

<div class="section-{{ section.id }}-margin">
  {% unless section.settings.section_heading == blank %}
    <div class="container px-4 md:px-0">
      <div class="flex flex-col mx-auto justify-start md:justify-center w-full md:w-[780px]  lg:text-left">
        <h1 class="heading-level-2 text-secondary text-center">
          {{ section.settings.section_heading }}
        </h1>
        {% unless section.settings.paragraph == blank %}
          <div class="mt-2 text-base text-center">
            {{ section.settings.paragraph }}
          </div>
        {% endunless %}
      </div>
    </div>
  {% endunless %}

  <div class="container px-4 md:px-0 mt-7 md:mt-16">
    <div class="main-block-wraper grid grid-flow-row auto-rows-max gap-6 content-stretch">
      {%- for block in section.blocks -%}
        {% if block.type == 'image_block' %}
          <div class="image-block overflow-hidden rounded-3xl {% if forloop.index == 1 %}col-span-2 row-span-3 flex h-full{%  elsif forloop.index == 2 %}col-span-3 col-start-3{% else %}col-span-3 row-span-2 col-start-3 row-start-2{% endif %}">
            {%- if block.settings.image -%}
              {%- liquid
                assign height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round
                assign sizes = '100vw'
                assign widths = '375, 550, 750, 1100'
                assign fetch_priority = 'auto'
                assign classes = 'lazy-load blur-loading'
                if section.index == 1
                  assign fetch_priority = 'high'
                endif
              -%}
              {%- if forloop.first %}
                {{
                  block.settings.image
                  | image_url: width: 3840
                  | image_tag:
                    height: height,
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority,
                    class: classes
                }}
              {%- else -%}
                {{
                  block.settings.image
                  | image_url: width: 3840
                  | image_tag: loading: 'lazy', height: height, sizes: sizes, widths: widths, class: classes
                }}
              {%- endif -%}
            {%- else -%}
              {{- 'image' | placeholder_svg_tag: 'w-[60px] h-[60px] border border-gray-7 rounded-xl' -}}
            {%- endif -%}
          </div>
        {% endif %}
      {% endfor %}
    </div>

    <div class="richtext-block">
      {%- for block in section.blocks -%}
        {% if block.type == 'richtect_block' %}
          <div class="richtext-custom my-16">
            <div class="content text-4xl font-bold">{{- block.settings.richtext_content -}}</div>
          </div>
        {% endif %}
      {% endfor %}
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-10 my-16">
      {%- for block in section.blocks -%}
        {% if block.type == 'content_grid_block' %}
          <div class="content-grid-block py-6 border-t text-gray-5">
            <p class="text-gray-5 text-base mb-2 ">{{ block.settings.subtext }}</p>
            <h2 class="section-heading-40">{{ block.settings.title }}</h2>
          </div>
        {% endif %}
      {%- endfor -%}
    </div>
  </div>
  {% unless section.settings.btn_text == blank %}
    <div class="button-block mt-16 flex justify-center">
      <a
        href="{{- section.settings.btn_url -}}"
        target="{{- section.settings.btn_target -}}"
        class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if section.settings.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
      >
        {% if section.settings.btn_variant != 'solid_primary' %}
          <span class="text-primary-gradient text-sm font-bold">
            {{- section.settings.btn_text -}}
          </span>
        {% else %}
          {{- section.settings.btn_text -}}
        {% endif %}
      </a>
    </div>
  {% endunless %}
</div>

{% schema %}
{
  "name": "Collage",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "section_heading",
      "label": "Section heading",
      "default": "Styku Academy"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add description"
    },
    {
      "type": "header",
      "content": "Section Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 32
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Primary button",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Btton URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        { "value": "_blank", "label": "Open in a New Tab" },
        { "value": "_self", "label": "Open in Same Tab" }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "image_block",
      "name": "Image",
      "limit": 6,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    },
    {
      "type": "richtect_block",
      "name": "Richtext block",
      "limit": 2,
      "settings": [
        {
          "type": "richtext",
          "id": "richtext_content",
          "label": "Richtect block"
        }
      ]
    },
    {
      "type": "content_grid_block",
      "name": "Content grid block",
      "limit": 6,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "text",
          "id": "subtext",
          "label": "Subtext"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collage",
      "blocks": [
        {
          "type": "image_block"
        },
        {
          "type": "image_block"
        },
        {
          "type": "image_block"
        },
        {
          "type": "richtect_block"
        },
        {
          "type": "content_grid_block"
        },
        {
          "type": "content_grid_block"
        },
        {
          "type": "content_grid_block"
        }
      ]
    }
  ]
}
{% endschema %}
