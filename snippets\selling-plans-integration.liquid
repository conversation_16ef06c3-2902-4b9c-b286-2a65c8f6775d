<div class="selling_plan_app_container flex flex-col md:flex-row gap-6 md:gap-2 justify-between hidden">
  <div class="button-group">
    {% for variant in product.variants %}
      {% if variant.selling_plan_allocations.size > 0 %}
        {% assign group_ids = variant.selling_plan_allocations | map: 'selling_plan_group_id' | uniq %}
        {% for group_id in group_ids %}
          {% liquid
            assign allocations = variant | map: 'selling_plan_allocations' | where: 'selling_plan_group_id', group_id
            if forloop.first
              assign first_selling_plan_group = true
            else
              assign first_selling_plan_group = false
            endif
          %}
          <div class="flex w-fit h-fit gap-2 p-1.5 bg-[#1B2842] rounded-lg">
            {% for allocation in allocations %}
              {% liquid
                if forloop.first
                  assign price = allocation.price | divided_by: 12 | money_without_trailing_zeros | escape
                  assign compare_price = allocation.compare_at_price | money_without_trailing_zeros | escape
                else
                  assign price = allocation.price | money_without_trailing_zeros | escape
                endif
              %}
              {% liquid
                if forloop.first and product.requires_selling_plan and first_selling_plan_group
                  assign plan_checked = 'checked'
                else
                  assign plan_checked = null
                endif
              %}
              <div>
                <input
                  onchange="onProductOptionChange(this, event)"
                  name="selling_plan"
                  type="radio"
                  {% if variant.available == false %}
                    disabled
                  {% endif %}
                  value="{{ allocation.selling_plan.id }}"
                  id="selling_plan_{{ allocation.selling_plan.id }}"
                  name="purchaseOption_{{ section.id }}_{{ variant.id }}"
                  data-radio-type="selling_plan"
                  data-selling-plan-id="{{ allocation.selling_plan.id }}"
                  data-selling-plan-name="{{ allocation.selling_plan.name }}"
                  data-selling-plan-adjustment="{{ allocation.selling_plan.price_adjustments.size }}"
                  data-variant-price="{{ price }}"
                  data-variant-compare-at-price="{{ compare_price }}"
                  class="product-option peer hidden"
                  {{ plan_checked }}
                >
              </div>
            {% endfor %}
          </div>
        {% endfor %}
      {% endif %}
    {% endfor %}
  </div>
</div>
