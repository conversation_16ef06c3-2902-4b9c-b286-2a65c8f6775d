{% comment %}
  @param btn_text: String
  @param shopify_hosted_video_link: Object (expected to have a `sources` array with at least one item containing a `url` field)
  @param external_video_link: Object (expected to have `type` and `id` fields for YouTube or Vimeo links)
  @param video_btn_variant: String (valid values: 'solid_primary', 'outline_primary')
  Usage: {% render 'watch-now-button' btn_text: '', shopify_hosted_video_link: '', external_video_link: '', video_btn_variant: '' %}
{% endcomment %}

{%- liquid
  if shopify_hosted_video_link != blank
    assign video_src = shopify_hosted_video_link.sources[0].url
  elsif external_video_link.type == 'youtube'
    assign video_src = 'https://www.youtube.com/embed/' | append: external_video_link.id
  elsif external_video_link.type == 'vimeo'
    assign video_src = 'https://player.vimeo.com/video/' | append: external_video_link.id
  endif
-%}

<div class="video-button-block inline-flex">
  <video-button
    class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if video_btn_variant == 'solid_primary' %}button-primary-gradient{% else %}button-primary-gradient-outline{% endif %}"
    data-url="{{ video_src }}"
  >
    {% if video_btn_variant != 'solid_primary' %}
      <span class="text-primary-gradient text-sm font-bold">
        {{- btn_text -}}
      </span>
    {% else %}
      {{- btn_text -}}
    {% endif %}
  </video-button>
</div>
