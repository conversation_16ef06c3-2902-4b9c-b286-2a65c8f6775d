document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('resetPassword');
  const email = document.getElementById('email');
  const password = document.getElementById('password');
  const errorMessageGroup = document.querySelector('.error-message-group');
  const minimumCharactersPasswordMessage = errorMessageGroup.querySelector('.minimum-characters-message');
  const numberPasswordMessage = errorMessageGroup.querySelector('.number-message');
  const upperLowerCaseMessage = errorMessageGroup.querySelector('.upper-lowercase-message');
  const specialCharactersMessage = errorMessageGroup.querySelector('.special-characters-message');
  const submitButton = document.getElementById('submitButton');
  const successMessage = window.successMessage;
  const exceptionMessage = window.errorMessage;

  // Reload browser on popstate
  reloadOnPopState();

  const emailFromHistory = readFromHistoryState('email');

  if (!emailFromHistory) {
    window.location.href = '/';
  } else {
    // Automatically populate the email field
    email.value = emailFromHistory ?? '';

    // Event listener for password input
    password.addEventListener('input', function () {
      enableSubmit();

      const passwordValue = password.value;

      // Check if password length is at least 8 characters
      if (isValidLength(passwordValue, 8)) {
        updatePasswordValidationMessage(minimumCharactersPasswordMessage, true);
        updatePasswordValidationIcon(minimumCharactersPasswordMessage, true);
      } else {
        updatePasswordValidationMessage(minimumCharactersPasswordMessage, false);
        updatePasswordValidationIcon(minimumCharactersPasswordMessage, false);
      }

      if (checkUpperAndLowerCase(passwordValue)) {
        updatePasswordValidationMessage(upperLowerCaseMessage, true);
        updatePasswordValidationIcon(upperLowerCaseMessage, true);
      } else {
        updatePasswordValidationMessage(upperLowerCaseMessage, false);
        updatePasswordValidationIcon(upperLowerCaseMessage, false);
      }

      // Check if password contains at least one digit
      if (containsDigit(passwordValue)) {
        updatePasswordValidationMessage(numberPasswordMessage, true);
        updatePasswordValidationIcon(numberPasswordMessage, true);
      } else {
        updatePasswordValidationMessage(numberPasswordMessage, false);
        updatePasswordValidationIcon(numberPasswordMessage, false);
      }

      // Checks the special characters from the set [@,$,!,%,*,#,?,&]
      if (containsSpecialChar(passwordValue)) {
        updatePasswordValidationMessage(specialCharactersMessage, true);
        updatePasswordValidationIcon(specialCharactersMessage, true);
      } else {
        updatePasswordValidationMessage(specialCharactersMessage, false);
        updatePasswordValidationIcon(specialCharactersMessage, false);
      }
    });

    // Function to enable/disable submit button based on form validity
    function enableSubmit() {
      submitButton.disabled = !isResetPasswordValid(password);
    }

    // Function to check if password is valid
    function isResetPasswordValid(input) {
      // Should be 8 characters long and contain one digit and at least one special character (@$!%*#?&)
      return isValidLength(input.value, 8) && containsDigit(input.value) && containsSpecialChar(input.value);
    }

    // Add an event listener to the form submission event
    form.addEventListener('submit', async function (event) {
      event.preventDefault();

      submitButton.disabled = true;
      spinnerIcon.classList.remove('hidden');

      const requestBody = {
        email: emailFromHistory,
        password: password.value,
      };

      try {
        const profile = { email: requestBody.email };
        const resetPasswordPayload = API_PAYLOADS.RESET_PASSWORD;
        resetPasswordPayload.body = JSON.stringify(requestBody);

        const resetPasswordResponse = await fetchData(resetPasswordPayload);

        if (resetPasswordResponse.ok) {
          const resetPasswordResponseData = await resetPasswordResponse.json();

          switch (resetPasswordResponseData.status) {
            case SUCCESS_CODE.SUCCESS:
              showToastMessage(successMessage.passwordChangedSuccessfully);
              deleteHistoryStateItem('forgetPasswordString');
              updateHistoryState(profile, PAGE_URLS.SHOPIFY_LOGIN);
              break;
            case ERROR_CODES.WEAK_PASSWORD:
              showToastMessage(exceptionMessage.weakPasswordMissingLetter);
              break;
            default:
              showToastMessage(exceptionMessage.somethingWentWrong);
              break;
          }
        } else {
          showToastMessage(exceptionMessage.somethingWentWrong);
        }
      } catch (error) {
        console.error('Error while securing profile:', error);
        showToastMessage(exceptionMessage.somethingWentWrong);
      } finally {
        submitButton.disabled = false;
        spinnerIcon.classList.add('hidden');
      }
    });
  }
});
