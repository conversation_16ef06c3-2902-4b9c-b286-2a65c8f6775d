<div class="cart-header flex items-center {% if cart.item_count != 0 %}mb-5 gap-4{% else %}flex-col justify-center{% endif %} {% if border %} border border-gray-2 p-4 rounded-lg{% endif %}">
  {% unless cart.item_count == 0 %}
    <h2 class="text-3xl md:text-4xl font-bold text-secondary">
      {{ 'cart.title' | t }}
    </h2>
  {% endunless %}
  <div class="cart-icon-block">
    {{- 'icon-cart-circle.svg' | inline_asset_content -}}
  </div>
  {% unless cart.item_count != 0 %}
    <h2 class="{{ title_classes }}">{{ 'cart.empty.title' | t }}</h2>
    <p class="paragraph-text w-full text-center {{ paragraph_width }}">{{ 'cart.empty.message' | t }}</p>
  {% endunless %}
</div>
