<div class="partner-logo-wrapper">
  <div class="text-xs text-gray-8 mb-4 text-center">
    {{- heading -}}
  </div>
  <div class="w-full inline-flex flex-nowrap overflow-hidden">
    <ul
      id="partner-logos"
      class="flex items-center gap-4 md:gap-0 justify-center md:justify-start animate-infinite-scroll"
    >
      {% for item in metaobject_type.values %}
        <li class="partner-logo max-h-24 w-auto mx-2 md:mx-8">
          <img
            src="{{ item.logo.value  | image_url: width: 120 }}"
            alt="{{ item.partner_name.value }}"
            loading="lazy"
            width="120"
            height=""
            class="max-h-16 md:max-h-24 w-auto max-w-none"
            {% unless settings.filter_gray == blank %}
              style="filter: grayscale({{ settings.filter_gray }}) opacity({{ settings.filter_opacity }});"
            {% endunless %}
          >
        </li>
      {% else %}
        {% for i in (1..15) %}
          <li class="partner-logo">
            <p class="text-base">Logo {{ i }}</p>
          </li>
        {% endfor %}
      {% endfor %}
    </ul>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const logos = document.getElementById('partner-logos');
    const duplicate = logos.cloneNode(true);
    duplicate.setAttribute('aria-hidden', 'true');
    logos.parentElement.appendChild(duplicate);
  });
</script>
