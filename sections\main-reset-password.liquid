<div class="container px-4 lg:px-0">
  <div class="main-wrapper mb-7 mt-9 md:mb-0 md:mt-[60px]">
    <div class="header-block mb-11 text-center">
      <div class="logo-block flex justify-center mb-5">{{- 'styku-logo.svg' | inline_asset_content -}}</div>
      <h2 class="text-[28px] font-bold text-secondary leading-9 mb-2.5">
        {{ 'sections.reset_password.title' | t }}
      </h2>
      <p class="paragraph-text">{{ 'sections.reset_password.description' | t }}</p>
    </div>
    <form id="resetPassword" class="form-block mx-auto max-w-[600px] space-y-6">
      <div class="input-block">
        <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.email.label' | t }}</label>
        <input
          type="email"
          name="email"
          id="email"
          readonly
          class="bg-white opacity-55 select-none border cursor-not-allowed text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
          placeholder="{{ 'input.email.placeholder' | t }}"
        >
      </div>
      <div class="input-block">
        {% assign password_placeholder = 'input.password.placeholder' | t %}
        <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.password.label' | t }}</label>
        {% render 'toggle-password-visibility', id: 'password', placeholder: password_placeholder %}
      </div>
      <div class="error-message-block !mt-4">
        {% render 'password-error-messages' %}
      </div>
      <div class="button-block">
        <button
          id="submitButton"
          class="button-primary-gradient cursor-pointer flex justify-center items-center gap-4 !text-base w-full !py-3"
          disabled
          role="button"
        >
          {% render 'white-spinner-icon' %}
          <span>{{ 'general.customer.next' | t }}</span>
        </button>
      </div>
    </form>
  </div>
</div>
<script src="{{ 'toggle-password-visibility.js' | asset_url }}" defer></script>
<script src="{{ 'reset-password.js' | asset_url }}" defer></script>
{% schema %}
{
  "name": "Reset password",
  "class": "index-section"
}
{% endschema %}
