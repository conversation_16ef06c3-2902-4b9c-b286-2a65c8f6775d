document.addEventListener('DOMContentLoaded', () => {
  const timeline = document.querySelector('.timeline');
  const timelineItems = document.querySelectorAll('.timeline-item');
  const greyLine = document.querySelector('.default-line');
  const lineToDraw = document.querySelector('.draw-line');
  const timelineElements = document.querySelectorAll('.timeline li');

  // Adjust the height of the grey background line
  function adjustLineHeights() {
    if (!timeline || !greyLine || timelineItems.length === 0) return;

    const totalHeight = timeline.offsetHeight;
    const lastItemHeight = timelineItems[timelineItems.length - 1].offsetHeight;

    // Adjust height of the grey line to avoid overlapping the last item
    greyLine.style.height = `${totalHeight - lastItemHeight + 12}px`;
  }

  // Draw the animated line based on scroll
  function drawDynamicLine() {
    if (!lineToDraw || !timeline || !greyLine) return;

    const greyLineHeight = greyLine.offsetHeight;
    const scrollPosition = window.scrollY;
    const windowCenter = window.innerHeight / 2;

    // Use getBoundingClientRect + scrollY to get distance from top of document
    const timelineTop = timeline.getBoundingClientRect().top + window.scrollY;

    if (scrollPosition >= timelineTop - windowCenter) {
      let lineHeight = scrollPosition - timelineTop + windowCenter;

      // Limit the animated line height to the background line height
      lineToDraw.style.height = `${Math.min(lineHeight, greyLineHeight)}px`;
    }

    updateTimelineItems();
  }

  // Highlight timeline items when the animated line passes them
  function updateTimelineItems() {
    const lineBottom = lineToDraw.getBoundingClientRect().bottom;

    timelineElements.forEach((item) => {
      const itemTop = item.getBoundingClientRect().top;
      item.classList.toggle('in-view', lineBottom > itemTop);
    });
  }

  // Initial height adjustment
  adjustLineHeights();

  // Recalculate heights and redraw line on resize and scroll
  window.addEventListener('resize', adjustLineHeights);
  window.addEventListener('scroll', drawDynamicLine);
});
