// Product options selector - Listen for change events
window.onProductOptionChange = async (input) => {
  const form = input.closest('form');
  const variantProduct = form.querySelector('[name="variant_product"]:checked');
  const selectedSellingPlan = form.querySelector('[name="selling_plan"]:checked');

  if (selectedSellingPlan) {
    handleSellingPlans(selectedSellingPlan);
  } else {
    handleProductVariants(variantProduct);
  }
};

// Handle selling plans
function handleSellingPlans(selectedSellingPlan) {
  const isMonthlyPlanSelected = selectedSellingPlan.dataset.sellingPlanName === 'Monthly';

  if (isMonthlyPlanSelected) {
    document.getElementById('savingsMessage').classList.add('invisible');
    document.getElementById('billedPrice').classList.add('invisible');
  } else {
    document.getElementById('savingsMessage').classList.remove('invisible');
    document.getElementById('billedPrice').classList.remove('invisible');
  }

  selectedSellingPlan.closest('.price-selector-block').querySelector('.product-price .price-text').textContent =
    selectedSellingPlan.dataset.variantPrice;
}

// Handle variants product
function handleProductVariants(variantProduct) {
  variantProduct.closest('form').querySelector('[name="id"]').value = variantProduct.dataset.variantId;

  variantProduct.closest('.price-selector-block').querySelector('.product-price .price-text').textContent =
    variantProduct.dataset.variantPrice;
}
