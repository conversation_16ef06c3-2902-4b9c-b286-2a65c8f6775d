<ul class="custom-scrollbar max-h-[224px] flex flex-col gap-1.5 overflow-y-auto">
  {% assign options = shop.metaobjects[metaobject_namespace].values | sort_natural: 'custom_sort' %}

  {% for option in options %}
    {% assign item_value = option %}
    {% assign item_label = option %}

    {% if value_key contains '.' %}
      {% assign parts = value_key | split: '.' %}
      {% assign item_value = option[parts[0]][parts[1]] %}
    {% else %}
      {% assign item_value = option[value_key] %}
    {% endif %}

    {% if title_key contains '.' %}
      {% assign parts = title_key | split: '.' %}
      {% assign item_label = option[parts[0]][parts[1]] %}
    {% else %}
      {% assign item_label = option[title_key] %}
    {% endif %}

    <li
      class="{{- item_class | default: 'link-item select-option' }} cursor-pointer select-none py-1 text-base text-gray-800"
      data-value="{%  if item_value == 'All Facilities' %}{{- 'all_facilities' -}}{%  else %}{{- item_value -}}{% endif %}"
      role="option"
      tabindex="-1"
    >
      {{- item_label -}}
    </li>
  {% endfor %}
</ul>
