{% comment %}
  @param title: The title of the section to be displayed.
  @param paragraph: A brief description or paragraph for the section.
  @param tags: An array of features, such as 'Circumferences, Volumes, Surface area, Regional circumferences, Cross-sections, Regional volumes, Regional Surface Areas', split into individual tags.
  @param video_link: The link to the video file (e.g., .mp4) to be displayed within the video frame.
  @param tab_settings: The settings object used for the button-block render.
  Usage: {% render 'tab-content', title: '', paragraph: '', tags: [], video_link: '', tab_settings: '' %}
{% endcomment %}

<div class="flex flex-col justify-between max-w-xl mx-auto lg:pt-11 lg:flex-row md:px-14 lg:max-w-screen-xl gap-7 md:gap-0">
  <div class="content-block lg:max-w-lg md:mt-7">
    <h2 class="max-w-lg mb-3 text-[24px] leading-tight md:text-[34px] md:leading-10 font-bold text-secondary">
      {{- title -}}
    </h2>
    <p class="paragraph-text mb-3">{{- paragraph -}}</p>
    <div class="tags-block flex flex-wrap gap-2.5">
      {% for tag in tags %}
        <span class="bg-primary-light text-secondary text-sm capitalize rounded-lg py-2 px-2.5">{{- tag -}}</span>
      {% endfor %}
    </div>
    <div class="mt-4 md:mt-8">
      {% render 'button-block', settings_context: tab_settings %}
    </div>
  </div>
  <div
    class="video-frame-section object-cover object-top w-full h-auto mx-auto lg:h-auto xl:mr-24 md:max-w-x relative overflow-hidden"
    style="
      background: url({{ 'frame.png' |  asset_url }});
      background-repeat: no-repeat, repeat;
      width: 332px;
      height: 545px;
    "
  >
    <video
      autoplay
      muted
      loop
      playsinline
      webkit-playsinline
      src="{{ video_link }}"
      class="w-full border-none h-full absolute top-3 rounded-[50px]"
      type="video/mp4"
    ></video>
  </div>
</div>
