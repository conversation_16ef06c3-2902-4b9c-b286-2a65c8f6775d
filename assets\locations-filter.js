document.addEventListener('DOMContentLoaded', () => {
  const dropdowns = document.querySelectorAll('.filter-dropdown');

  function closeAllDropdowns(exclude = null) {
    dropdowns.forEach((dropdown) => {
      if (dropdown !== exclude) {
        const options = dropdown.querySelector('.filter-options');
        const toggle = dropdown.querySelector('button');
        if (options && toggle) {
          options.style.display = 'none';
          toggle.setAttribute('aria-expanded', 'false');
        }
      }
    });
  }

  dropdowns.forEach((dropdown) => {
    const toggleButton = dropdown.querySelector('button');
    const optionsContainer = dropdown.querySelector('.filter-options');
    const selectedDisplay = dropdown.querySelector('span[id$="SelectedLabel"]');
    const options = optionsContainer.querySelectorAll('li[data-value]');
    let currentIndex = -1;
    let selectedValue = null;

    if (!toggleButton || !optionsContainer || !selectedDisplay) return;

    const openDropdown = () => {
      closeAllDropdowns(dropdown);
      optionsContainer.style.display = 'block';
      toggleButton.setAttribute('aria-expanded', 'true');

      // Remember last selected item
      currentIndex = Array.from(options).findIndex((opt) => opt.dataset.value === selectedValue);

      if (currentIndex >= 0) {
        highlightOption(currentIndex);
      }
    };

    const closeDropdown = () => {
      optionsContainer.style.display = 'none';
      toggleButton.setAttribute('aria-expanded', 'false');
      currentIndex = -1;
    };

    const selectOption = (index) => {
      const option = options[index];
      if (!option) return;

      const value = option.dataset.value;
      const label = option.textContent.trim();

      options.forEach((opt) => opt.classList.remove('selected-option')); // Clear previous selection highlight
      option.classList.add('selected-option'); // Add highlight to the newly selected option

      selectedValue = value;
      selectedDisplay.textContent = label;
      selectedDisplay.classList.replace('text-gray-5', 'text-gray-8');
      closeDropdown();

      dropdown.dispatchEvent(
        new CustomEvent('filterDropdown:change', {
          detail: {
            id: dropdown.id,
            value,
            label,
          },
          bubbles: true,
        })
      );
    };

    const highlightOption = (index) => {
      if (index < 0 || index >= options.length) return;

      options.forEach((opt, i) => {
        opt.classList.toggle('focus-option-item', i === index);
      });

      options[index].scrollIntoView({ block: 'nearest' });
    };

    toggleButton.addEventListener('click', (e) => {
      e.stopPropagation();
      const isOpen = optionsContainer.style.display === 'block';
      isOpen ? closeDropdown() : openDropdown();
    });

    toggleButton.addEventListener('keydown', (e) => {
      const key = e.key;

      switch (key) {
        case 'ArrowDown':
          e.preventDefault();
          if (optionsContainer.style.display !== 'block') openDropdown();
          currentIndex = Math.min(currentIndex + 1, options.length - 1);

          highlightOption(currentIndex);
          break;
        case 'ArrowUp':
          e.preventDefault();
          if (optionsContainer.style.display !== 'block') openDropdown();
          currentIndex = Math.max(currentIndex - 1, 0);
          highlightOption(currentIndex);
          break;
        case 'Enter':
          e.preventDefault();
          if (currentIndex >= 0) selectOption(currentIndex);
          break;
        case 'Escape':
          e.preventDefault();
          closeDropdown();
          break;
      }
    });

    options.forEach((option, index) => {
      option.addEventListener('click', () => selectOption(index));
      option.setAttribute('tabindex', '-1');
    });
  });

  // Todo: We will reuse this for the input search

  // Close when clicking outside
  document.addEventListener('click', () => closeAllDropdowns());
});
