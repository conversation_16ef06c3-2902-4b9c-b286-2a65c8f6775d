@tailwind base;
@tailwind components;
@tailwind utilities;

.paragraph-text {
  @apply text-base font-normal text-gray-8;
}

.paragraph-text-responsive {
  @apply text-sm font-normal text-gray-8 md:text-base;
}

.button-primary-gradient {
  @apply rounded-full bg-gradient-primary px-8 py-[6px] text-sm font-semibold leading-[22px] text-white;
}

.button-secondary {
  @apply h-9 rounded-full bg-[#EBEBEB] px-8 py-[6px] text-sm font-semibold leading-[22px] text-gray-8;
}

.button-primary-light {
  @apply rounded-full bg-[#F5D4DD] px-8 py-[6px] text-base font-bold;
}

.input-field {
  @apply paragraph-text block w-full rounded-lg border border-gray-2 bg-white p-2.5 px-3.5 focus:border-gray-2 focus:outline-none;
}

/* Defined the heading Level */
.heading-level-1 {
  @apply text-[28px] font-bold leading-tight text-secondary sm:text-[36px] sm:leading-[44px] md:text-[40px] md:leading-[48px];
}

.heading-level-2 {
  @apply text-2xl font-bold text-secondary sm:text-[28px] sm:leading-[38px] md:text-[36px] md:leading-[44px];
}

.heading-level-3 {
  @apply text-2xl font-bold text-secondary md:text-[28px] md:leading-9;
}

.heading-level-4 {
  @apply text-[22px] font-bold leading-7 text-secondary;
}

.section-heading-40 {
  @apply text-[28px] font-bold leading-tight md:text-[40px] md:leading-[48px];
}
