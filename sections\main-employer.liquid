{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }

    .employer-banner h2.title {
      max-width: 450px;
    }
  }

  .employer-banner .button-block a,
  .employer-banner .video-button-block video-button {
    padding: 12px 65px !important;
    font-size:16px !important;
  }

  .employer-banner .button-block a span  {
    font-size:16px !important;
  }

  @media only screen and (max-width: 768px) {
     .list-block-content .button-block {
       margin-top: 28px !important;
     }

    .employer-banner h2.title {
      font-size:28px;
      line-height:1.2;
    }

    .employer-banner .button-block a,
    .employer-banner .video-button-block video-button {
      padding: 12px 32px !important;
      font-size:14px !important;
      width:100%;
    }

    .employer-banner .button-wrapper {
      gap:16px
    }

    .employer-banner .button-wrapper {
      display: grid !important;
      grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }

    .partner-logo-wrapper {
      margin-top:48px !important;
    }

    .image-block-media-with-text {
      order: 2;
    }

    .block-main-wrapper .image-overflow img {
      width: 157px !important;
      height: 358px !important;
    }

    .list-block-content h2 {
      font-size: 20px !important;
    }
    .list-block-main-wrapper {
      gap:40px;
    }
    .media-with-text-inner-wrapper {
      margin-top: 40px !important;
      margin-bottom: 40px !important;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }
{%- endstyle -%}

<div class="employer-banner bg-bolor section-{{ section.id }}-margin">
  <div class="container py-5 md:py-8">
    <div class="flex flex-col md:grid-cols-2 justify-center md:justify-between gap-10 {{ section.settings.desktop_block_gap }} {{ section.settings.media_align_desktop }}">
      <div class="flex items-center justify-center w-full md:w-1/2">
        {% if section.settings.img != blank %}
          {%- liquid
            assign sizes = '100vw'
            assign widths = '750, 1100, 1500, 1780'
            assign fetch_priority = 'high'
          -%}
          {{
            section.settings.img
            | image_url: width: 3840
            | image_tag:
              loading: 'lazy',
              sizes: sizes,
              widths: widths,
              fetchpriority: fetch_priority,
              class: 'w-full lazy-load blur-loading'
          }}
        {%- else -%}
          {{- 'image' | placeholder_svg_tag: 'w-[430px] h-[270px] border border-gray-7 rounded-xl' -}}
        {%- endif -%}
      </div>
      <div class="order-first md:order-none px-4 lg:px-0 flex flex-col justify-start md:justify-center lg:text-left w-full md:w-1/2">
        {% unless section.settings.title == blank %}
          <h2 class="title {{ section.settings.title_font_size }} mb-2">
            {{ section.settings.title }}
          </h2>
        {% endunless %}
        {% unless section.settings.description == blank %}
          <div class="[&_div]:space-y-5">
            <div class="description paragraph-text {{ section.settings.paragraph_class }}">
              {{ section.settings.description }}
            </div>
          </div>
        {% endunless %}
        <div class="button-wrapper flex flex-wrap {{ section.settings.button_gap }} mt-6 md:mt-8">
          {% unless section.settings.btn_text == blank %}
            <div class="button-block ">
              <a
                href="{{- section.settings.btn_url -}}"
                target="{{- section.settings.btn_target -}}"
                class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if section.settings.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
              >
                {% if section.settings.btn_variant != 'solid_primary' %}
                  <span class="text-primary-gradient text-sm font-bold">
                    {{- section.settings.btn_text -}}
                  </span>
                {% else %}
                  {{- section.settings.btn_text -}}
                {% endif %}
              </a>
            </div>
          {% endunless %}
          {% unless section.settings.video_btn_text == blank %}
            {% render 'watch-now-button',
              btn_text: section.settings.video_btn_text,
              shopify_hosted_video_link: section.settings.shopify_hosted_video,
              external_video_link: section.settings.external_video,
              video_btn_variant: section.settings.video_btn_variant
            %}
          {% endunless %}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Employer",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 32
    },
    {
      "type": "select",
      "id": "media_align_desktop",
      "label": "Media alignment on desktop",
      "default": "md:flex-row",
      "options": [
        { "value": "md:flex-row", "label": "Left" },
        { "value": "md:flex-row-reverse", "label": "Right" }
      ]
    },
    {
      "type": "select",
      "id": "desktop_block_gap",
      "label": "Set Gap for the desktop",
      "default": "md:gap-4",
      "options": [
        { "value": "md:gap-4", "label": "Gap-4" },
        { "value": "md:gap-6", "label": "gap-6" },
        { "value": "md:gap-8", "label": "gap-8" },
        { "value": "md:gap-10", "label": "gap-10" },
        { "value": "md:gap-12", "label": "gap-12" },
        { "value": "md:gap-14", "label": "gap-14" },
        { "value": "md:gap-16", "label": "gap-16" },
        { "value": "md:gap-20", "label": "gap-20" },
        { "value": "md:gap-24", "label": "gap-24" },
        { "value": "md:gap-28", "label": "gap-28" },
        { "value": "md:gap-32", "label": "gap-32" }
      ]
    },
    {
      "type": "header",
      "content": "Image"
    },
    {
      "type": "image_picker",
      "id": "img",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "html",
      "id": "title",
      "label": "Title",
      "default": "Media with text"
    },
    {
      "type": "select",
      "id": "title_font_size",
      "label": "Title font-size",
      "default": "heading-level-2",
      "options": [
        { "value": "heading-level-1", "label": "Level 1" },
        { "value": "heading-level-2", "label": "Level 2" },
        { "value": "heading-level-3", "label": "Level 3" },
        { "value": "heading-level-4", "label": "Level 4" }
      ]
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description (optional)",
      "default": "<p>Styku conducts a comprehensive 3D body scan, capturing millions of precise data points in just 35 seconds. Research shows that Styku’s body fat percentage estimates align within 2% of DEXA scan results, a gold standard in body composition, underscoring its exceptional accuracy.</p>"
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Primary button",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Btton URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        { "value": "_blank", "label": "Open in a New Tab" },
        { "value": "_self", "label": "Open in Same Tab" }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    },
    {
      "type": "header",
      "content": "Video button block",
      "info": "[Note:] Use only one of the options. When both External and Shopify-hosted are added, the Shopify-hosted option will be used."
    },
    {
      "type": "text",
      "id": "video_btn_text",
      "label": "Button text",
      "default": "Watch Now"
    },
    {
      "type": "video_url",
      "id": "external_video",
      "label": "External video",
      "accept": ["youtube", "vimeo"],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
    },
    {
      "type": "video",
      "id": "shopify_hosted_video",
      "label": "Shopify-hosted video"
    },
    {
      "type": "select",
      "id": "video_btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    },
    {
      "type": "select",
      "id": "button_gap",
      "label": "Open file",
      "default": "gap-3",
      "options": [
        { "value": "gap-2", "label": "Gap-2" },
        { "value": "gap-3", "label": "Gap-3" },
        { "value": "gap-4", "label": "Gap-4" },
        { "value": "gap-5", "label": "Gap-5" },
        { "value": "gap-6", "label": "Gap-6" }
      ]
    }
  ]
}
{% endschema %}
