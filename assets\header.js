document.addEventListener('DOMContentLoaded', function () {
  window.onscroll = function () {
    const header = document.querySelector('#shopify-section-header header');
    const stickyThreshold = header.offsetTop;

    if (window.pageYOffset > stickyThreshold) {
      header.classList.add('sticky-header');
    } else {
      header.classList.remove('sticky-header');
    }
  };

  const openMobileNavigation = document.querySelector('#openMobileNavigation');
  const closeMobileNavigation = document.querySelector('#closeMobileNavigation');
  const navbarCollapse = document.querySelector('#navbarCollapse');
  const profileDropdown = document.getElementById('profileDropdown');
  const logoutLinks = document.querySelectorAll('a[href^="/account/logout"]');
  const accountLinkBlock = document.querySelectorAll('.header-profile-link-block .account-links');
  const menuDrawerNavigationContainer = document.querySelector('.menu-drawer-navigation-container');
  const popupCartItemEl = document.querySelector('#mobile-popup-cart-item');
  const popupCartTargetEl = document.querySelector('[data-drawer-target="mobile-popup-cart-item"]');
  const popupCartHideBtnEl = document.querySelector('[data-drawer-hide="mobile-popup-cart-item"]');
  const cartItemBlockEl = popupCartItemEl?.querySelector('.responsive-cart-item-block');

  // Disable automatic scroll restoration
  if ('scrollRestoration' in history) {
    history.scrollRestoration = 'manual';
  }

  // Function to adjust height of the navigation wrapper based on viewport
  function adjustHeight(reduceHeight = 0) {
    return `${window.innerHeight - reduceHeight}px`;
  }

  window.addEventListener('resize', function () {
    const isMobile = window.matchMedia('(max-width: 768px)').matches;

    if (isMobile) {
      navbarCollapse.style.height = adjustHeight();
      menuDrawerNavigationContainer.style.height = adjustHeight(88);
      if (cartItemBlockEl) {
        cartItemBlockEl.style.height = adjustHeight(68);
        cartItemBlockEl.style.overflowY = 'scroll';
      }
    } else {
      navbarCollapse.style.height = '';
      menuDrawerNavigationContainer.style.height = '';
      if (cartItemBlockEl) {
        cartItemBlockEl.style.height = '';
        cartItemBlockEl.style.overflowY = '';
      }
    }
  });

  // Event listener to handle opening the cart popup box on responsive devices
  popupCartTargetEl.addEventListener('click', () => {
    cartItemBlockEl.style.height = adjustHeight(68);
    document.body.style.overflowY = 'hidden';
  });

  // Event listener to handle closing the cart popup box on responsive devices
  popupCartHideBtnEl.addEventListener('click', () => {
    cartItemBlockEl.style.height = '';
    document.body.style.overflowY = '';
  });

  // Event listener to handle opening the mobile navigation menu
  openMobileNavigation.addEventListener('click', () => {
    navbarCollapse.style.height = adjustHeight();
    menuDrawerNavigationContainer.style.height = adjustHeight(88);
    document.body.style.overflowY = 'hidden';
    navbarCollapse.classList.add('block');
    navbarCollapse.classList.remove('hidden');
  });

  // Sub-menu
  if (window.matchMedia('(max-width: 768px)').matches) {
    document.querySelectorAll('.submenu-item').forEach((el) => {
      el.querySelector('a').addEventListener('click', (event) => {
        el.classList.toggle('submenu-item-active');
        event.target.classList.toggle('active');
        event.target.querySelector('.caret-icon svg').classList.toggle('active');
        el.querySelector('.submenu').classList.toggle('hidden');
      });
    });
  }

  // Event listener to handle closing the mobile navigation menu
  closeMobileNavigation.addEventListener('click', () => {
    navbarCollapse.style.height = '';
    menuDrawerNavigationContainer.style.height = '';
    navbarCollapse.classList.remove('block');
    document.body.style.overflowY = '';
    closeMobileNavigation.classList.remove('navbarTogglerActive');
    navbarCollapse.classList.add('hidden');
  });

  // Add event listener to account links
  if (accountLinkBlock) {
    accountLinkBlock.forEach((linkItems) => {
      linkItems.addEventListener('click', function (event) {
        event.preventDefault();
        const href = this.getAttribute('href');
        location.href = `${location.origin}${PAGE_URLS.ACCOUNT}${href}`;

        if (location.pathname === PAGE_URLS.ACCOUNT) {
          location.reload();
        }

        // Remove 'active-link' class from all links and add it to the clicked link
        accountLinkBlock.forEach((item) => item.classList.remove('active-link'));
        this.classList.add('active-link');
      });

      // Check if the href matches the hash URL and add 'active-link' class
      const href = linkItems.getAttribute('href');
      if (href === window.location.hash) {
        linkItems.classList.add('active-link');
      }
    });
  }

  // Add event listeners to each profile dropdown item to hide the dropdown menu when clicked
  if (profileDropdown) {
    const profileDropdownItems = profileDropdown.querySelectorAll('.profile-dropdown-item');
    profileDropdownItems.forEach((link) => {
      link.addEventListener('click', function () {
        profileDropdown.classList.add('hidden');
      });
    });
  }

  // Attach click event listener to each logout link element and perform logout functionality
  logoutLinks.forEach((link) => {
    link.addEventListener('click', logoutHandler);
  });

  // Function to perform logout functionality
  async function logoutHandler(event) {
    event.preventDefault();
    const exceptionMessage = window.errorMessage;

    try {
      toggleFullScreenPreloader(true);
      const accessToken = getLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      const stykuResponse = await fetchData(API_PAYLOADS.SIGN_OUT(accessToken));
      validateResponse(stykuResponse, exceptionMessage.somethingWentWrong);

      const shopifyResponse = await fetchData(API_PAYLOADS.SHOPIFY_CUSTOMER_SIGN_OUT);
      validateResponse(shopifyResponse, exceptionMessage.somethingWentWrong);

      deleteLocalStorageItem(LOCAL_STORAGE_KEY.LOGGED_IN_USER);
      deleteLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      window.location.replace(PAGE_URLS.SHOPIFY_LOGIN);
    } catch (error) {
      showToastMessage(exceptionMessage.somethingWentWrong);
      console.error('Error during sign out:', error);
    } finally {
      toggleFullScreenPreloader(false);
    }
  }

  function validateResponse(response, errorMessage) {
    if (response.status !== SUCCESS_CODE.SUCCESS) {
      throw new Error(errorMessage);
    }
  }
});
