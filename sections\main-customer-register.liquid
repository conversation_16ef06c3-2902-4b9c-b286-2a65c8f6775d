{% liquid
  for link in linklists.footer.links
    if link.url contains 'terms'
      assign terms_and_condictions_url = link.url
    elsif link.url contains 'privacy'
      assign privacy_policy_url = link.url
    endif
  endfor
%}

<div class="container px-4 lg:px-0">
  <div class="main-wrapper mb-7 mt-9 md:mb-0 md:mt-[60px]">
    <div class="header-block mb-11 text-center">
      <div class="logo-block flex justify-center mb-5">{{- 'styku-logo.svg' | inline_asset_content -}}</div>
      <h2 id="newProfileTitle" class="text-[28px] font-bold text-secondary leading-9 mb-2.5">
        {{ 'sections.secure_account.title' | t }}
      </h2>
      <h2 id="multipleScansExistingProfileTitle" class="hidden text-[28px] font-bold text-secondary leading-9 mb-2.5">
        {{ 'sections.secure_account.multiple_scans_existing_profile_title_html' | t: scans: '' }}
      </h2>
      <h2 id="singleScanExistingProfileTitle" class="hidden text-[28px] font-bold text-secondary leading-9 mb-2.5">
        {{ 'sections.secure_account.single_scan_or_existing_profile_title_html' | t: scans: '' }}
      </h2>
      <p class="paragraph-text">{{ 'sections.secure_account.description' | t }}</p>
    </div>
    <form id="secureAccount" class="form-block mx-auto max-w-[600px] space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-4">
        <div class="input-block ">
          <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.first_name.label' | t }}</label>
          <input
            type="text"
            id="firstName"
            name="firstName"
            placeholder="{{ 'input.first_name.placeholder' | t }}"
            class="bg-white border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
          >
        </div>
        <div class="input-block">
          <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.last_name.label' | t }}</label>
          <input
            type="text"
            id="lastName"
            name="lastName"
            placeholder="{{ 'input.last_name.placeholder' | t }}"
            class="bg-white border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
          >
        </div>
      </div>
      <div class="input-block">
        <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.email.label' | t }}</label>
        <div class="relative">
          <input
            type="email"
            name="email"
            id="email"
            readonly
            class="opacity-55 select-none border cursor-not-allowed text-base border-gray-2 text-secondary bg-gray-1 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
            placeholder="{{ 'input.email.placeholder' | t }}"
          >
          <button type="button" class="absolute top-0 end-0 py-2.5 px-3 opacity-50">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
              <circle cx="12" cy="12.8594" r="10.4" stroke="#959595" stroke-width="1.2" />
              <path
                d="M8.6875 10.16C8.6875 8.24616 10.2721 6.85938 12.0339 6.85938C13.7957 6.85938 15.3085 8.24616 15.3085 10.16C15.3085 11.6335 14.0904 12.1049 12.9705 13.3428C12.3329 14.0476 11.9991 14.8611 11.9991 15.8611M12.0339 17.3611V18.8611"
                stroke="#959595" stroke-width="1.2" />
            </svg>
          </button>
        </div>
      </div>
      <div class="input-block">
        <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.new_password.label' | t }}</label>
        {% assign password_placeholder = 'input.new_password.placeholder' | t %}
        {% render 'toggle-password-visibility', id: 'password', placeholder: password_placeholder %}
        <div class="error-message-block">
          <p class="text-sm text-gray-5 my-2">{{ 'input.new_password.error_message.title' | t }}</p>
          {% render 'password-error-messages' %}
        </div>
      </div>
      <div class="input-block">
        {% assign confirm_password_placeholder = 'input.confirm_password.placeholder' | t %}
        <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.confirm_password.label' | t }}</label>
        {% render 'toggle-password-visibility', id: 'confirmPassword', placeholder: confirm_password_placeholder %}
        <div class="error-message-block mt-2 absolute">
          <div class="error-message-group">
            <p class="valid-password text-sm flex gap-2 items-center hidden">
              <span class="cross-icon"></span>
              {{ 'input.confirm_password.message.valid_password' | t }}
            </p>
            <p class="invalid-password text-sm flex gap-2 items-center hidden">
              <span class="cross-icon"></span>
              {{- 'input.confirm_password.message.invalid_password' | t }}
            </p>
          </div>
        </div>
      </div>
      <div class="input-block flex items-center !mt-8">
        <input
          id="policyCheckboxAccepted"
          type="checkbox"
          value=""
          class="w-4 h-4 bg-gray-100 border-gray-2 rounded-lg focus:ring-gray-2"
        >
        <label for="red-checkbox" class="ms-2 paragraph-text">
          {{
            'general.customer.policy_message_html'
            | t: terms_and_conditions_url: terms_and_condictions_url, privacy_policy_url: privacy_policy_url
          }}
        </label>
      </div>
      <div class="button-block !mt-8">
        <button
          id="submitButton"
          class="button-primary-gradient flex justify-center items-center gap-4 !text-base w-full !py-3"
          disabled
          role="button"
        >
          {% render 'white-spinner-icon' %}
          <span>{{ 'general.customer.next' | t }}</span>
        </button>
      </div>
    </form>
  </div>
</div>

<script src="{{ 'toggle-password-visibility.js' | asset_url }}" defer></script>
<script src="{{ 'customer-register.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "Customer register",
  "class": "index-section"
}
{% endschema %}
