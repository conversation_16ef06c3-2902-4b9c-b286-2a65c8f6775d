{% liquid
  assign bundle_product_type = 'bundle-product'
  assign health_product_type = 'health-products'

  assign has_restricted_product_in_cart = false

  for cart_item in cart_items
    if cart_item.product.type == bundle_product_type or cart_item.product.type == health_product_type
      assign has_restricted_product_in_cart = true
    endif
  endfor

  for link in linklists.footer.links
    if link.url contains 'terms'
      assign terms_and_condictions_url = link.url
    elsif link.url contains 'privacy'
      assign privacy_policy_url = link.url
    endif
  endfor
%}
{% if has_restricted_product_in_cart %}
  <div class="flex flex-col gap-3 bg-gray-1 border border-gray-2 py-5 px-4 rounded-lg" role="alert">
    <p class="alert-message-text text-gray-8 text-xs">
      {{- 'cart.alert_message.age_restriction_html' | t -}}
    </p>
    <p class="alert-message-text text-gray-8 text-xs">
      {{- 'cart.alert_message.notice_for_new_york_city_residents_html' | t -}}
    </p>
    <p class="alert-message-text text-gray-8 text-xs">
      {{-
        'cart.alert_message.policy_message_html'
        | t: terms_and_conditions_url: terms_and_condictions_url, privacy_policy_url: privacy_policy_url
      -}}
    </p>
  </div>
{% endif %}
