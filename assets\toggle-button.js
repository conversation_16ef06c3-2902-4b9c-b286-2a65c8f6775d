class ToggleButton extends HTMLElement {
  constructor() {
    super();
    this.addEventListener('click', this.handleClick.bind(this));
  }

  connectedCallback() {
    this.style.cursor = 'pointer';
    this.style.userSelect = 'none';
  }

  handleClick() {
    const { seeMore, seeLess } = window.utilsString.general;
    const parentTable = this.closest('.comparison-table-wrapper');
    const parentCard = this.closest('.comparison-cards');
    const productsCard = this.closest('.products-lists');
    const pricingTable = this.closest('.pricing-table-wrapper');
    const pricingCard = this.closest('.product-list');

    const buttonTextElem = this.querySelector('p');
    this.querySelector('img').classList.toggle('rotate');

    if (parentTable) {
      this.toggleTableRows(parentTable, buttonTextElem, seeMore, seeLess, true);
    } else if (parentCard) {
      this.toggleCardRows(parentCard, buttonTextElem, seeMore, seeLess);
    } else if (productsCard) {
      this.toggleProductsFeaturesRows(productsCard, buttonTextElem, seeMore, seeLess);
    } else if (pricingTable) {
      this.toggleProductsPricingFeaturesRows(pricingTable, buttonTextElem, seeMore, seeLess);
    } else if (pricingCard) {
      this.togglePricingCardFeaturesRows(pricingCard, buttonTextElem, seeMore, seeLess);
    }
  }

  // Toggles the visibility of table rows and updates the button state.
  toggleTableRows(table, buttonText, seeMore, seeLess) {
    const rows = table.querySelectorAll('table tbody tr.toggle-rows');
    const showRows = table.querySelector('table tbody').dataset.showRows;
    const updateBottomBorder = table.querySelector(`table tbody tr:nth-child(${showRows}) td:nth-child(2)`);
    rows.forEach((row) => row.classList.toggle('hidden'));
    this.updateButtonState(buttonText, [updateBottomBorder], seeMore, seeLess);
    this.smoothScrollToSection(table, 80);
  }

  // Toggles the visibility of card rows and updates the button state.
  toggleCardRows(card, buttonText, seeMore, seeLess) {
    const rows = card.querySelectorAll('.compare-cards .toggle-rows');
    rows.forEach((row) => row.classList.toggle('hidden'));
    this.updateButtonState(buttonText, null, seeMore, seeLess);
    this.smoothScrollToSection(card, 80);
  }

  // Toggles the visibility of product pricing feature rows and updates the button state.
  toggleProductsPricingFeaturesRows(table, buttonText, seeMore, seeLess) {
    const rows = table.querySelectorAll('table tbody tr.toggle-rows');
    const showRows = table.querySelector('table tbody').dataset.showRows;

    const columns = [2, 3, 4].map((index) =>
      table.querySelector(`table tbody tr:nth-child(${showRows}) td:nth-child(${index})`)
    );

    rows.forEach((row) => row.classList.toggle('hidden'));
    this.updateButtonState(buttonText, columns, seeMore, seeLess);
  }

  // Toggles the visibility of pricing card rows and updates the button state.
  togglePricingCardFeaturesRows(card, buttonText, seeMore, seeLess) {
    const rows = card.querySelectorAll('.product-card .toggle-rows');
    rows.forEach((row) => row.classList.toggle('hidden'));
    this.updateButtonState(buttonText, null, seeMore, seeLess);
  }

  // Toggles the visibility of product feature rows and updates the button state.
  toggleProductsFeaturesRows(card, buttonText, seeMore, seeLess) {
    const rows = card.querySelectorAll('.products-list .toggle-rows');
    rows.forEach((row) => row.classList.toggle('hidden'));
    this.updateButtonState(buttonText, null, seeMore, seeLess);
  }

  // Updates the button text based on the current state (See More / See Less).
  updateButtonState(buttonText, borderElements, seeMore, seeLess) {
    if (buttonText.innerText === seeMore) {
      buttonText.innerText = seeLess;

      if (Array.isArray(borderElements)) {
        this.removeBorder(borderElements);
      }
    } else {
      buttonText.innerText = seeMore;

      if (Array.isArray(borderElements)) {
        this.removeStyleAttribute(borderElements);
      }
    }
  }

  // Removes specific border styles from the provided elements.
  removeBorder(borderElements) {
    borderElements.forEach((element) => {
      element.style.borderRadius = '0';
      element.style.borderBottom = '0';

      const hrLine = element.querySelector('.horizontal-line');
      if (hrLine) {
        hrLine.style.display = 'block';
      }
    });
  }

  // Removes the inline style attributes from the provided elements.
  removeStyleAttribute(borderElements) {
    borderElements.forEach((element) => {
      element.removeAttribute('style');

      const hrLine = element.querySelector('.horizontal-line');
      if (hrLine) {
        hrLine.removeAttribute('style');
      }
    });
  }

  // Smoothly scrolls to the closest section containing the given element, offset by a certain amount.
  smoothScrollToSection(element, offset = 0) {
    const targetSection = element.closest('.shopify-section');
    if (targetSection) {
      const top = targetSection.getBoundingClientRect().top + window.scrollY - offset;
      window.scrollTo({ top, behavior: 'smooth' });
    }
  }
}

// Define the custom element
customElements.define('toggle-rows-button', ToggleButton);
