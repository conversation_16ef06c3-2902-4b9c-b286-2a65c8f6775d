{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  .content-block-styku-academy .content strong.soft-black {
    color: var(--color-secondary);
  }

  .content-block-styku-academy .content span.soft-black {
    color: var(--color-gray-7);
  }

  .content-block-styku-academy .content strong.soft-white {
    color: var(--color-white);
  }

  .content-block-styku-academy .content span.soft-white {
    color: var(--color-gray-3);
  }

  .slideshow-styku-academy .owl-dots {
    background: var(--color-gray-1) !important;
    border-radius: 26px !important;
    padding: 12px !important;
    display: inline-flex !important;
    position: absolute;
    bottom: -15%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .slideshow-styku-academy .owl-dots .owl-dot.active span {
    width: 12px !important;
    height: 12px !important;
    background: var(--color-gray-6) !important;
  }

  .slideshow-styku-academy .owl-dots .owl-dot.active span {
    background: var(--color-gray-6) !important;
    width: 64px !important;
  }
   .custom-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, auto));
    justify-content: center;
    align-items: center;
    column-gap:64px;
    row-gap:16px;
   }
   .custom-grid img {
    height: 100px;
    width: auto;
   }
{%- endstyle -%}

<div class="section-{{ section.id }}-margin">
  {% unless section.settings.section_heading == blank %}
    <div class="container px-4 md:px-0">
      <div class="flex flex-col mx-auto justify-start md:justify-center w-full md:w-[85%]  lg:text-left">
        <h1 class="heading-level-2 text-secondary text-center">
          {{ section.settings.section_heading }}
        </h1>
        {% unless section.settings.paragraph == blank %}
          <div class="mt-2 text-base text-center">
            {{ section.settings.paragraph }}
          </div>
        {% endunless %}
      </div>
    </div>
  {% endunless %}

  <div class="{%  if section.settings.layout == 'container' %}container px-4 md:px-0 {%  else %}w-full {% endif %} mt-7 md:mt-16">
    <div class="slideshow-styku-academy owl-carousel owl-theme">
      {%- for block in section.blocks -%}
        {% case block.type %}
          {% when 'slide' %}
            <div class="relative grid grid-cols-1 overflow-hidden rounded-3xl">
              <style>
                #slide-{{ section.id }}-{{ forloop.index }} {
                  min-height: {{ block.settings.block_height | default: '120' }}px;
                  {% unless block.settings.bg_color == blank %}
                    background: {{ block.settings.bg_color }};
                  {% endunless %}
                }
                #slide-margin-{{ section.id }}-{{ forloop.index }} {
                  {% if block.settings.alignment_top > 0 %}
                    margin-top: {{ block.settings.alignment_top }}px;
                  {% endif %}
                  {% if block.settings.alignment_bottom > 0 %}
                    margin-bottom: {{ block.settings.alignment_bottom }}px;
                  {% endif %}
                  {% if block.settings.alignment_left > 0 %}
                    margin-left: {{ block.settings.alignment_left }}px;
                  {% endif %}

                  {% if block.settings.alignment_right > 0 %}
                    margin-right: {{ block.settings.alignment_right }}px;
                  {% endif %}
                }
              </style>
              <div class="image-block">
                {%- if block.settings.image -%}
                  {%- liquid
                    assign height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round
                    assign sizes = '100vw'
                    assign widths = '375, 550, 750, 1100, 1500, 1780'
                    assign fetch_priority = 'auto'
                    assign classes = 'lazy-load blur-loading'
                    if section.index == 1
                      assign fetch_priority = 'high'
                    endif
                  -%}
                  {%- if forloop.first %}
                    {{
                      block.settings.image
                      | image_url: width: 3840
                      | image_tag:
                        height: height,
                        sizes: sizes,
                        widths: widths,
                        fetchpriority: fetch_priority,
                        class: classes
                    }}
                  {%- else -%}
                    {{
                      block.settings.image
                      | image_url: width: 3840
                      | image_tag: loading: 'lazy', height: height, sizes: sizes, widths: widths
                    }}
                  {%- endif -%}
                {%- else -%}
                  {%- assign placeholder_slide = forloop.index | modulo: 2 -%}
                  {%- if placeholder_slide == 1 -%}
                    {{ 'hero-apparel-2' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- else -%}
                    {{ 'hero-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                {%- endif -%}
              </div>
              {% if block.settings.external_video != blank %}
                {%- liquid
                  if block.settings.external_video.type == 'youtube'
                    assign video_src = 'https://www.youtube.com/embed/' | append: block.settings.external_video.id
                  elsif block.settings.external_video.type == 'vimeo'
                    assign video_src = 'https://player.vimeo.com/video/' | append: block.settings.external_video.id
                  endif
                -%}

                <div class="absolute inset-0 flex items-center justify-center size-full bg-black bg-opacity-35">
                  <video-button
                    data-url="{{ video_src }}"
                    aria-label="Play Video"
                    class="group"
                  >
                    <img
                      src="{{ 'primary-play-icon.svg' | asset_url }}"
                      width="132"
                      height="132"
                      class="flex items-center justify-center size-32 transition duration-300 transform rounded-full shadow-2xl group-hover:scale-110"
                    >
                  </video-button>
                </div>
              {% else %}
                <div
                  class="content-block-styku-academy flex w-full absolute p-9 {{ block.settings.box_align }} {{ block.settings.box_items_align }} {{ block.settings.content_align }}"
                  id="slide-{{ section.id }}-{{ forloop.index }}"
                >
                  <div
                    class="content-block-styku-academy"
                    id="slide-margin-{{ section.id }}-{{ forloop.index }}"
                  >
                    {%- if block.settings.subheading != blank -%}
                      <div
                        class="content text-xl min-w-[380px] max-w-[468px]"
                        {% unless forloop.index == 2 %}
                          style="width:390px"
                        {% endunless %}
                      >
                        {{- block.settings.subheading -}}
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              {% endif %}
            </div>
        {% endcase %}
      {%- endfor -%}
    </div>
    <div class="mt-32 custom-grid">
      {%- for block in section.blocks -%}
        {% case block.type %}
          {% when 'logo_block' %}
            {%- if block.settings.logo -%}
              {%- liquid
                assign height = block.settings.logo.width | divided_by: block.settings.logo.aspect_ratio | round
                assign sizes = '100vw'
                assign widths = '375, 550, 750'
                assign fetch_priority = 'auto'
                assign classes = 'lazy-load blur-loading'
                if section.index == 1
                  assign fetch_priority = 'high'
                endif
              -%}
              {%- if forloop.first %}
                {{
                  block.settings.logo
                  | image_url: width: 750
                  | image_tag:
                    height: height,
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority,
                    class: classes
                }}
              {%- else -%}
                {{
                  block.settings.logo
                  | image_url: width: 750
                  | image_tag: loading: 'lazy', height: height, sizes: sizes, widths: widths
                }}
              {%- endif -%}
            {%- else -%}
              {{- 'image' | placeholder_svg_tag: 'w-[60px] h-[60px] border border-gray-7 rounded-xl' -}}
            {%- endif -%}
        {% endcase %}
      {%- endfor -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Styku Academy",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "section_heading",
      "label": "Section heading",
      "default": "Styku Academy"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add description"
    },
    {
      "type": "header",
      "content": "Section Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 32
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "w-full",
          "label": "Full width"
        },
        {
          "value": "container",
          "label": "Container"
        }
      ],
      "default": "container",
      "label": "Layout"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Slide image"
        },
        {
          "type": "html",
          "id": "subheading",
          "label": "Sub heading"
        },
        {
          "type": "color_background",
          "id": "bg_color",
          "label": "Background color"
        },
        {
          "type": "select",
          "id": "box_align",
          "options": [
            {
              "value": "top-0",
              "label": "Top"
            },
            {
              "value": "bottom-0",
              "label": "Bottom"
            }
          ],
          "default": "bottom-0",
          "label": "Place content box"
        },
        {
          "type": "select",
          "id": "box_items_align",
          "options": [
            {
              "value": "items-baseline",
              "label": "Align to Top"
            },
            {
              "value": "items-center",
              "label": "Align to Center"
            },
            {
              "value": "items-end",
              "label": "Align to Bottom"
            }
          ],
          "default": "items-end",
          "label": "Align Box Content"
        },
        {
          "type": "range",
          "id": "alignment_top",
          "label": "Adjust Content from Top",
          "min": 0,
          "max": 200,
          "unit": "px",
          "step": 2,
          "default": 0
        },
        {
          "type": "range",
          "id": "alignment_left",
          "label": "Adjust Content from Left",
          "min": 0,
          "max": 200,
          "unit": "px",
          "step": 2,
          "default": 0
        },
        {
          "type": "range",
          "id": "alignment_right",
          "label": "Adjust Content from Right",
          "min": 0,
          "max": 200,
          "unit": "px",
          "step": 2,
          "default": 0
        },
        {
          "type": "range",
          "id": "alignment_bottom",
          "label": "Adjust Content from Bottom",
          "min": 0,
          "max": 200,
          "unit": "px",
          "step": 2,
          "default": 0
        },

        {
          "type": "select",
          "id": "content_align",
          "options": [
            {
              "value": "justify-normal",
              "label": "Left"
            },
            {
              "value": "justify-center text-center",
              "label": "Center"
            },
            {
              "value": "justify-end",
              "label": "Right"
            }
          ],
          "default": "justify-normal",
          "label": "Place content"
        },
        {
          "type": "range",
          "id": "block_height",
          "label": "Content block height",
          "min": 16,
          "max": 280,
          "unit": "px",
          "step": 4,
          "default": 120
        },
        {
          "type": "header",
          "content": "Video button block",
          "info": "[Note:] Use only one of the options. When both content is added, and the youTube video is uploaded then Vidoe UI will use."
        },
        {
          "type": "video_url",
          "id": "external_video",
          "label": "External video",
          "accept": ["youtube", "vimeo"]
        }
      ]
    },
    {
      "type": "logo_block",
      "name": "Logo block",
      "limit": 10,
      "settings": [
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Upload image",
          "info": "Choose an image to display in the image block."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Styku Academy",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
