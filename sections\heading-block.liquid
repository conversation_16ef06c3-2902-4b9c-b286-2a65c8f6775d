{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
{%- endstyle -%}

<div
  class="section-{{ section.id }}-margin pt-10 md:pt-20"
  style="background: linear-gradient(180deg, #EEEFF3 0%, #FFF 100%);"
>
  <div class="container px-4 lg:px-0">
    <div class="heading-block mb-8 md:mb-12 text-center mx-auto flex flex-col items-center">
      <h1 class="section-heading-40 text-secondary w-full">
        {{ section.settings.section_title }}
      </h1>
      <div class="mt-2 md:mt-3 text-base w-full">
        {{ section.settings.section_description }}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Heading block",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "html",
      "id": "section_title",
      "label": "Title",
      "default": "Predict 20+ Disease Risks"
    },
    {
      "type": "richtext",
      "id": "section_description",
      "label": "Description (optional)",
      "default": "<p>No labs. No needles. No doctor visits.</p>"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    }
  ],

  "presets": [
    {
      "name": "Heading block"
    }
  ]
}
{% endschema %}
