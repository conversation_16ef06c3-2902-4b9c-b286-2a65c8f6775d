.horizontal-line {
  width: 100%;
  height: 2px;
  margin-top: 16px;
  margin-bottom: 16px;
  border-top: 1px solid #ebebeb;
}

.card-overlap-text {
  position: absolute;
  top: -8px;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-transform: uppercase;
  font-weight: 500;
  width: fit-content;
  background-color: white;
  padding: 0 8px;
  z-index: 1;
}

.products-list.standard-3d-body-scan {
  background-color: white;
  border: 4px solid var(--color-gray-3);
  max-width: min-content;
  margin-top: 34px;
}

.products-list.card-type-standard-with-buy-now-button .buy-now-button-block button {
  background: var(--gradient-primary);
}

.products-list.card-type-standard-with-buy-now-button .buy-now-button-block button .text-primary-gradient {
  color: var(--color-white) !important;
}

.buy-now-button-block {
  margin-top: auto;
}

.products-list {
  display: flex;
  flex-direction: column;
  min-width: 25%;
  width: 100%;
  border-radius: 12px;
  padding: 14px;
  height: auto;
}

.products-list {
  background-color: white;
}

.price-block {
  display: flex;
  gap: 20px;
  width: 100%;
  border-radius: 12px;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  min-height: 172px;
  gap: 12px;
}

@media screen and (max-width: 768px) {
  .products-list {
    min-width: 100%;
    margin-top: 0;
  }
}
