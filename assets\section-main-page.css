.rte p:not(:last-child) {
  margin-bottom: 16px;
}

.rte:after {
  clear: both;
  content: '';
  display: block;
}

.rte > *:first-child {
  margin-top: 0;
}

.rte > *:last-child {
  margin-bottom: 0;
}

.rte table {
  table-layout: fixed;
}

@media screen and (min-width: 750px) {
  .rte table td {
    padding-left: 1.2rem;
    padding-right: 1.2rem;
  }
}

.rte ul,
.rte ol {
  list-style-position: inside;
  padding-left: 2rem;
}

.rte li {
  list-style: inherit;
}

.rte li:last-child {
  margin-bottom: 0;
}

.rte blockquote {
  display: inline-flex;
}

.rte blockquote > * {
  margin: -0.5rem 0 -0.5rem 0;
}

/* Custom styling for the opt-out form */
#pc--opt-out-form-container {
  margin-top: 48px;
}

.pc--opt-out-form-container {
  max-width: 100% !important;
}

.pc--opt-out-form-container .pc--opt-out-form-button {
  border-radius: 6px !important;
  border: 1px solid var(--color-primary) !important;
  background-clip: text !important;
  color: transparent !important;
  background-image: var(--gradient-primary) !important;
}

.pc--opt-out-form-wrapper .pc--opt-out-form-field label {
  margin-bottom: 6px !important;
}

.pc--opt-out-form-wrapper .pc--opt-out-form-field input {
  width: 100% !important;
  border-radius: 0.5rem !important;
  border: 1px solid var(--color-gray-2) !important;
  background-color: var(--color-white) !important;
  padding: 0.625rem 0.875rem !important;
  font-size: 1rem !important;
  color: var(--color-gray-8) !important;
  outline: none !important;
}

.pc--opt-out-form-wrapper .pc--opt-out-form-field input:focus {
  border: 1px solid var(--color-primary) !important;
  background: var(--color-white);
  box-shadow: 0 0 0 4px var(--color-blush-pink) !important;
}

.pc--opt-out-form-wrapper .pc--opt-out-form-field label {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: var(--color-secondary) !important;
}
