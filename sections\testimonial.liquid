{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  .bg-muted-seafoam {
    background: #EEF1F1;
  }
{%- endstyle -%}

<div class="testimonial-section section-{{ section.id }}-margin overflow-hidden py-12 md:py-16 lg:py-20">
  <div class="container px-4 lg:px-0">
    <div class="text-block w-full text-center mx-auto md:w-[572px]">
      <h1 class="section-heading-40 text-secondary font-bold">
        {{ section.settings.title }}
      </h1>
      <div class="mt-3 text-base">{{ section.settings.paragraph }}</div>
    </div>
  </div>
  <div class="content-block relative mt-8 md:mt-12">
    <div class="testimonial-slider owl-carousel owl-theme">
      {%- for block in section.blocks -%}
        <div class="text-gray-8 relative p-4 md:p-9 rounded-2xl min-h-80 grid grid-cols-1 content-stretch bg-muted-seafoam">
          <div class="text-sm">{{ block.settings.testimonial_content }}</div>
          <div class="justify-start items-end gap-4 flex">
            {%- if block.settings.image != blank -%}
              <div class="w-16 h-16 rounded-full border border-gray-2">
                <img
                  class="w-16 h-16 rounded-full"
                  width=""
                  height=""
                  alt="{{ block.settings.image.alt }}"
                  src="{{ block.settings.image | image_url: width: 66 }}"
                >
              </div>
            {%- else -%}
              <div class="bg-slate-200">{{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}</div>
            {%- endif -%}
            <div class="flex-col justify-start items-start gap-1 inline-flex">
              <div class="text-xl text-secondary font-semibold">
                {{ block.settings.client_name }}
              </div>
              <div class="text-sm text-gray-8">
                {{ block.settings.company_name }}
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</div>
{% schema %}
{
  "name": "Testimonial Slider",
  "class": "index-section",
  "max_blocks": 15,
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 80
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 32
    },
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Don’t take our word for it!"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Sub Heading",
      "default": "<p>Hear what others are saying about the benefits of a whole body scan from Healthpass.</p>"
    }
  ],
  "blocks": [
    {
      "type": "Testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "text",
          "id": "client_name",
          "label": "Client name",
          "default": "John Doe"
        },
        {
          "type": "text",
          "id": "company_name",
          "label": "Company name",
          "default": "Styku LLC"
        },
        {
          "type": "richtext",
          "id": "testimonial_content",
          "label": "Paragraph",
          "default": "<p>With Healthpass, we love the ability to offer a baseline screening to all. This serves as a health screening along with a measureable barometer for progress."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Testimonial Slider",
      "blocks": [
        {
          "type": "Testimonial"
        },
        {
          "type": "Testimonial"
        },
        {
          "type": "Testimonial"
        },
        {
          "type": "Testimonial"
        },
        {
          "type": "Testimonial"
        }
      ]
    }
  ]
}
{% endschema %}
