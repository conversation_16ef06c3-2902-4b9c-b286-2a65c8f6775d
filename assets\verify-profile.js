document.addEventListener('DOMContentLoaded', function () {
  const verifyProfileForm = document.getElementById('verifyProfile');
  const verifyProfileSubmit = document.getElementById('verifyProfileSubmit');
  const passwordForm = document.getElementById('passwordForm');
  const emailInputElem = document.getElementById('email');
  const passwordInput = document.getElementById('password-input');
  const passwordSubmitButton = document.getElementById('passwordSubmitButton');
  const passwordSubmitButtonSpinnerIcon = document.getElementById('passwordSubmitButtonSpinnerIcon');
  const verifyProfileSubmitSpinnerIcon = document.getElementById('verifyProfileSubmitSpinnerIcon');
  const forgetPassword = document.getElementById('forgetPassword');
  const exceptionMessage = window.errorMessage;
  const passwordHash = '#password';

  const emailFromHistory = readFromHistoryState('email');

  const checkoutString = readFromHistoryState('checkout');

  if (checkoutString) {
    document.getElementById('loginWelcomeMessage').classList.remove('hidden');
    document.getElementById('benefitsMessageBox').classList.remove('hidden');
    document.getElementById('loginMessage').classList.add('hidden');
  }

  if (emailFromHistory) {
    // Automatically populate the email field
    email.value = emailFromHistory ?? '';
    enableSubmit();
  }

  // Execute the logic on page load
  handleHashChange();

  // Reload browser on popstate
  reloadOnPopState();

  // Function to enable/disable submit button based on form validity
  function enableSubmit() {
    verifyProfileSubmit.disabled = !isEmailValid(emailInputElem.value);
  }

  emailInputElem.addEventListener('input', enableSubmit);

  // Event listener to redirect to home page when submit button is clicked
  verifyProfileForm.addEventListener('submit', async function (event) {
    event.preventDefault();

    verifyProfileSubmit.disabled = true;
    verifyProfileSubmitSpinnerIcon.classList.remove('hidden');

    const requestBody = {
      email: emailInputElem.value,
    };

    try {
      const profile = { email: requestBody.email };

      const verifyProfilePayload = API_PAYLOADS.VERIFY_PROFILE;
      verifyProfilePayload.body = JSON.stringify(requestBody);

      const response = await fetchData(verifyProfilePayload);

      if (response.ok) {
        const responseData = await response.json();

        switch (responseData.status) {
          case SUCCESS_CODE.SUCCESS:
            // If the user's profile is new, they will be directed to the email verification page
            updateHistoryState(profile, PAGE_URLS.EMAIL_VERIFY);
            break;
          case ERROR_CODES.ALREADY_SECURED:
            // If the user's profile is already secured with a password, they will be directed to the password page
            updateHistoryState(profile, passwordHash);
            break;
          case ERROR_CODES.INVALID_EMAIL:
            showToastMessage(exceptionMessage.invalidEmail);
            break;
          case ERROR_CODES.UNABLE_TO_SEND_EMAIL:
            showToastMessage(exceptionMessage.unableToSendEmail);
            break;
          default:
            showToastMessage(exceptionMessage.somethingWentWrong);
            break;
        }
      } else {
        showToastMessage(exceptionMessage.somethingWentWrong);
      }
    } catch (error) {
      console.error('Error while verifying profile:', error);
    } finally {
      verifyProfileSubmit.disabled = false;
      verifyProfileSubmitSpinnerIcon.classList.add('hidden');
    }
  });

  function handleHashChange() {
    if (window.location.hash === passwordHash) {
      const emailFromHistory = readFromHistoryState('email');

      if (!emailFromHistory) {
        window.location.replace(PAGE_URLS.SHOPIFY_LOGIN);
      } else {
        passwordInput.addEventListener('input', function () {
          passwordSubmitButton.disabled = !this.value;
        });

        passwordForm.addEventListener('submit', async function (event) {
          event.preventDefault();

          passwordSubmitButton.disabled = true;
          passwordSubmitButtonSpinnerIcon.classList.remove('hidden');

          const requestBody = {
            email: emailFromHistory,
            password: passwordInput.value,
          };

          try {
            const loginPayload = API_PAYLOADS.LOGIN;
            loginPayload.body = JSON.stringify(requestBody);

            const response = await fetchData(loginPayload);

            if (response.ok) {
              const responseData = await response.json();

              switch (responseData.status) {
                case SUCCESS_CODE.SUCCESS:
                  await shopifyCustomerLogin(emailFromHistory, passwordInput.value);
                  setLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, responseData.data.accessToken);

                  const checkoutStringFromHistory = readFromHistoryState('checkout');

                  if (checkoutStringFromHistory) {
                    handleQueryParamRedirect(QUERY_PARAMS_KEY.CHECKOUT_URL, PAGE_URLS.CART);
                    return;
                  }

                  // Function to handle redirection based on the presence of a specific query parameter
                  handleQueryParamRedirect(QUERY_PARAMS_KEY.CHECKOUT_URL, PAGE_URLS.ACCOUNT);
                  break;
                case ERROR_CODES.INVALID_USERNAME_OR_PASSWORD:
                  showToastMessage(exceptionMessage.invalidPassword);
                  break;
                default:
                  showToastMessage(exceptionMessage.somethingWentWrong);
                  break;
              }
            } else {
              showToastMessage(exceptionMessage.somethingWentWrong);
            }
          } catch (error) {
            console.error('Error while login:', error);
            showToastMessage(exceptionMessage.somethingWentWrong);
          } finally {
            passwordSubmitButton.disabled = false;
            passwordSubmitButtonSpinnerIcon.classList.add('hidden');
          }
        });

        // Event listener for the "Forget Password" button click event
        forgetPassword.addEventListener('click', async function (event) {
          await sendVerificationCode(event, passwordSubmitButton, emailFromHistory);

          const forgetPasswordString = { forgetPasswordString: window.utilsString.forgetPassword };
          updateHistoryState(forgetPasswordString, PAGE_URLS.EMAIL_VERIFY);
        });
      }
    }
  }

  // Listen for hash change events
  window.addEventListener('hashchange', handleHashChange);
});
