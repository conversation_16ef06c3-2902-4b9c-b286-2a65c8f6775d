{% comment %}
  Accepts:
  - settings_context: {Object} The settings object which contains:
    - btn_text: {String} The text for the button. (Leave blank to disable)
    - btn_url: {String} The URL the button links to.
    - btn_target: {String} (_self or _blank) Defines where to open the link.
    - btn_variant: {String} ('solid_primary' or 'outline_primary') The button style.
  Usage:
  {% render 'button-block', settings_context: block.settings or section.settings %}
{% endcomment %}

{% unless settings_context.btn_text == blank %}
  <div class="button-block inline-flex">
    <a
      href="{{- settings_context.btn_url -}}"
      target="{{- settings_context.btn_target -}}"
      class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-6 max-w-[180px] w-[180px] {% if settings_context.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }}{% else %}{{ 'button-primary-gradient-outline' }}{% endif %}"
    >
      {% if settings_context.btn_variant != 'solid_primary' %}
        <span class="text-primary-gradient text-sm font-bold">
          {{- settings_context.btn_text -}}
        </span>
      {% else %}
        {{- settings_context.btn_text -}}
      {% endif %}
    </a>
  </div>
{% endunless %}
