[{"name": "theme_info", "theme_name": "Healthpass", "theme_version": "1.0.0", "theme_author": "<PERSON><PERSON><PERSON>", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes", "theme_support_url": "https://support.shopify.com/"}, {"name": "t:settings_schema.version.name", "settings": [{"type": "text", "id": "version", "label": "t:settings_schema.version.settings.version_text.label"}]}, {"name": "t:settings_schema.logo.name", "settings": [{"type": "image_picker", "id": "logo", "label": "t:settings_schema.logo.settings.logo_image.label"}, {"type": "image_picker", "id": "favicon", "label": "t:settings_schema.logo.settings.favicon.label", "info": "t:settings_schema.logo.settings.favicon.info"}]}, {"name": "t:settings_schema.location.name", "settings": [{"type": "url", "id": "location_url", "label": "t:settings_schema.location.settings.location_url.label"}, {"type": "text", "id": "google_api_key", "label": "t:settings_schema.location.settings.google_api_key.label", "default": "AIzaSyBDVJlnISYCc76L3I5hMZ1vg_M7oXzU2Y4"}, {"type": "text", "id": "google_map_id", "label": "t:settings_schema.location.settings.google_map_id.label", "default": "8cf042776bd85394"}, {"type": "text", "id": "default_latitude", "label": "t:settings_schema.location.settings.default_latitude.label", "default": "34.0359864"}, {"type": "text", "id": "default_longitude", "label": "t:settings_schema.location.settings.default_longitude.label", "default": "-118.2328349"}, {"type": "text", "id": "filter_countries", "label": "t:settings_schema.location.settings.filter_countries.label", "info": "t:settings_schema.location.settings.filter_countries.info"}, {"type": "range", "id": "default_zoom", "label": "t:settings_schema.location.settings.default_zoom.label", "info": "Controls the initial zoom level when the map loads on desktop.", "min": 4, "max": 18, "step": 1, "default": 12}, {"type": "range", "id": "default_max_zoom", "label": "t:settings_schema.location.settings.default_max_zoom.label", "info": "Defines the highest zoom level allowed on desktop.", "min": 4, "max": 22, "step": 1, "default": 14}, {"type": "range", "id": "default_zoom_mobile", "label": "t:settings_schema.location.settings.default_zoom_mobile.label", "info": "Controls the initial zoom level when the map loads on mobile.", "min": 3, "max": 18, "step": 1, "default": 3}, {"type": "range", "id": "default_max_zoom_mobile", "label": "t:settings_schema.location.settings.default_max_zoom_mobile.label", "info": "Defines the highest zoom level allowed on mobile.", "min": 3, "max": 22, "step": 1, "default": 14}, {"type": "range", "id": "marker_center_map_zoom_level", "label": "t:settings_schema.location.settings.marker_center_map_zoom_level.label", "info": "Controls the zoom level when a marker is clicked.", "min": 3, "max": 22, "step": 1, "default": 10}, {"type": "range", "id": "marker_center_map_zoom_level_mobile", "label": "t:settings_schema.location.settings.marker_center_map_zoom_level_mobile.label", "info": "Controls the zoom level when a marker is clicked on mobile.", "min": 3, "max": 22, "step": 1, "default": 10}, {"type": "range", "id": "default_radius", "label": "t:settings_schema.location.settings.default_radius.label", "info": "Controls the radius of the search area in miles.", "min": 0, "max": 150, "step": 5, "default": 50}]}, {"name": "t:settings_schema.product.name", "settings": [{"type": "range", "id": "free_product_quantity", "label": "t:settings_schema.product.settings.free_product_quantity.label", "min": 1, "max": 10, "step": 1, "default": 2}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "t:settings_schema.typography.settings.header__1.content"}, {"type": "font_picker", "id": "type_header_font", "default": "poppins_n4", "label": "t:settings_schema.typography.settings.type_header_font.label", "info": "t:settings_schema.typography.settings.type_header_font.info"}, {"type": "header", "content": "t:settings_schema.typography.settings.header__2.content"}, {"type": "font_picker", "id": "type_body_font", "default": "poppins_n4", "label": "t:settings_schema.typography.settings.type_body_font.label", "info": "t:settings_schema.typography.settings.type_body_font.info"}, {"type": "font_picker", "id": "type_body_font_medium", "default": "poppins_n5", "label": "t:settings_schema.typography.settings.type_body_font_medium.label", "info": "t:settings_schema.typography.settings.type_body_font_medium.info"}, {"type": "font_picker", "id": "type_body_font_semibold", "default": "poppins_n6", "label": "t:settings_schema.typography.settings.type_body_font_semibold.label", "info": "t:settings_schema.typography.settings.type_body_font_semibold.info"}]}, {"name": "t:settings_schema.social-media.name", "settings": [{"type": "text", "id": "social_facebook_link", "default": "https://www.facebook.com/styku3D", "label": "t:settings_schema.social-media.settings.social_facebook_link.label"}, {"type": "text", "id": "social_twitter_link", "default": "https://twitter.com/Styku3D", "label": "t:settings_schema.social-media.settings.social_twitter_link.label"}, {"type": "text", "id": "social_instagram_link", "default": "https://www.instagram.com/styku3d", "label": "t:settings_schema.social-media.settings.social_instagram_link.label"}, {"type": "text", "id": "social_linkedin_link", "default": "https://www.linkedin.com/company/styku", "label": "t:settings_schema.social-media.settings.social_linkedin_link.label"}, {"type": "text", "id": "social_youtube_link", "default": "https://www.youtube.com/c/styku3d", "label": "t:settings_schema.social-media.settings.social_youtube_link.label"}]}]