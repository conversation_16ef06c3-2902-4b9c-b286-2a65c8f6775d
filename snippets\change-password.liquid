<div
  id="changePasswordModal"
  data-modal-backdrop="static"
  tabindex="-1"
  aria-hidden="true"
  class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full p-4 md:p-0 md:inset-0 h-[calc(100%-1rem)] max-h-full"
>
  <div class="relative p-4 md:p-6 w-full max-h-full bg-white rounded-lg shadow mx-auto max-w-[600px]">
    <div class="space-y-6">
      <div class="modal-header">
        <div class="flex items-center justify-between">
          <h3 class="heading-level-4">{{ 'general.customer.change_password' | t }}</h3>
          <button
            data-modal-hide="changePasswordModal"
            role="button"
            class="cursor-pointer hover:scale-125"
          >
            {{- 'icon-cross.svg' | inline_asset_content -}}
          </button>
        </div>
      </div>
      <div class="form-block">
        <form id="changePasswordForm" class="form-block mx-auto max-w-[600px] space-y-6">
          <div class="input-block">
            {% assign current_password_placeholder = 'input.current_password.placeholder' | t %}
            <label class="block mb-2 text-base text-secondary font-bold">
              {{- 'input.current_password.label' | t -}}
            </label>
            {% render 'toggle-password-visibility', id: 'oldPassword', placeholder: current_password_placeholder %}
          </div>
          <div class="input-block">
            <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.new_password.label' | t }}</label>
            {% assign password_placeholder = 'input.new_password.placeholder' | t %}
            {% render 'toggle-password-visibility', id: 'newPassword', placeholder: password_placeholder %}
            <div class="error-message-block !mt-4">
              <p class="text-sm text-gray-4 my-2">{{ 'input.new_password.error_message.title' | t }}</p>
              {% render 'password-error-messages' %}
            </div>
          </div>
          <div class="input-block">
            {% assign confirm_password_placeholder = 'input.confirm_password.placeholder' | t %}
            <label class="block mb-2 text-base text-secondary font-bold">
              {{- 'input.confirm_password.label' | t -}}
            </label>
            {% render 'toggle-password-visibility', id: 'confirmPassword', placeholder: confirm_password_placeholder %}
            <div class="error-message-block mt-1 absolute">
              <div class="error-message-group">
                <p class="valid-password text-sm flex gap-2 items-center hidden">
                  <span class="cross-icon"></span>
                  {{ 'input.confirm_password.message.valid_password' | t }}
                </p>
                <p class="invalid-password text-sm flex gap-2 items-center hidden">
                  <span class="cross-icon"></span>
                  {{- 'input.confirm_password.message.invalid_password' | t }}
                </p>
              </div>
            </div>
          </div>
          <div class="button-block !mt-8">
            <button
              id="submitChangePassword"
              type="submit"
              class="button-primary-gradient flex justify-center items-center gap-4 !text-base w-full !py-3"
              disabled
              role="button"
            >
              {% render 'white-spinner-icon' %}
              <span>{{ 'general.customer.submit' | t }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
