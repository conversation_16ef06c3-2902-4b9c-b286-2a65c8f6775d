{{ 'vertical-timeline.css' | asset_url | stylesheet_tag }}
{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="how-it-works-section section-{{ section.id }}-margin relative py-12 md:py-16 lg:py-20 overflow-hidden">
  <div class="overlap-icon-top-right absolute hidden md:block"></div>
  <div class="container px-4 lg:px-0">
    <div class="text-block w-full md:w-[70%] text-center mx-auto mb-9">
      <h1 class="section-heading-40 text-secondary font-bold">
        {{ section.settings.title }}
      </h1>
      <div class="mt-3 text-base">{{ section.settings.paragraph }}</div>
    </div>
    <div class="time-line-block ">
      <div class="timeline relative time-line-block">
        <ul class="mt-7">
          <span class="default-line"></span>
          <span class="draw-line"></span>

          {% for step in section.blocks %}
            <li class="timeline-item">
              <div class="relative md:pl-64 pb-20">
                <div class="flex items-start gap-10">
                  <div class="tileline-image-block flex-none sm:absolute {% if forloop.index == 2 %} -top-5{%  else %} -top-14{% endif %} {% if forloop.index > 2 %} md:w-[9.5rem] left-7{% else %} left-0 md:w-44{% endif %} translate-y-0.5 inline-flex items-center justify-center w-24 mb-3 sm:mb-0">
                    {%- if step.settings.image -%}
                      {%- liquid
                        assign height = step.settings.icon.width | divided_by: step.settings.image.aspect_ratio | round
                        assign sizes = '100vw'
                        assign widths = '375, 550, 750, 1054'
                        assign fetch_priority = 'high'
                      -%}
                      {{
                        step.settings.image
                        | image_url: width: 3840
                        | image_tag:
                          loading: 'lazy',
                          height: height,
                          sizes: sizes,
                          widths: widths,
                          fetchpriority: fetch_priority,
                          class: 'lazy-load blur-loading'
                      }}
                    {%- else -%}
                      {{- 'image' | placeholder_svg_tag: 'size-32 border border-gray-7 rounded-xl' -}}
                    {%- endif -%}
                  </div>
                  <div class="flex-1">
                    <div class="content-block">
                      <div class="heading-block mb-4 flex gap-2 font-bold items-center leading-none">
                        <span class="text-primary-heather_blush text-4xl md:text-4xl lg:text-[52px] leading-none font-bold">
                          {{ forloop.index }}
                        </span>
                        <span class="text-base md:text-[28px] leading-tight text-secondary font-bold">
                          {{ step.settings.heading }}
                        </span>
                      </div>
                      <p class="text-gray-8 text-sm">
                        {{ step.settings.description }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          {% endfor %}
        </ul>
      </div>
    </div>
    <div class="flex justify-center">
      {% render 'button-block', settings_context: section.settings %}
    </div>
  </div>

  <div class="overlap-icon absolute bottom-0 left-0 z-[1] hidden md:block">
    <svg xmlns="http://www.w3.org/2000/svg" width="162" height="709" viewBox="0 0 162 709" fill="none">
      <path opacity="0.4" d="M-328.389 29.6338C-252.21 219.149 -89.9976 620.163 -50.5815 708.105C371.225 535.392 104.028 -147.424 -328.389 29.6338Z" fill="#C5B1C4"/>
    </svg>
  </div>
</div>
<script src="{{ 'vertical-timeline.js' | asset_url }}" defer></script>

{% schema %}
{
  "name": "Dynamic Timeline",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 80
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title"
    },
    {
      "type": "textarea",
      "id": "paragraph",
      "label": "Section Paragraph"
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Book Scan",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Button URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        {
          "value": "_blank",
          "label": "Open in a New Tab"
        },
        {
          "value": "_self",
          "label": "Open in Same Tab"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "step",
      "name": "Step",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Step Icon"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "Step Heading"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Step Description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Dynamic Timeline"
    }
  ]
}
{% endschema %}
