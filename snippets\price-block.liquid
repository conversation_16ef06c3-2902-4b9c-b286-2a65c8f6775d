{% comment %}
  Accepts:
  - product: {Object} The product object containing pricing details.
  - forloop: {Object} The forloop object to generate unique class names.

  Usage:
  {% render 'price-block', product: product, forloop: forloop %}
{% endcomment %}

{% liquid
  assign plan_type = ''
  assign plan_label = ''
  assign plan_features = '' | split: ','

  assign in_person_consultation = 'product.one_person_consultation' | t
  assign preventative_body_scan = 'product.preventative_body_scan' | t
  assign preventative_body_scan_message_extra = 'product.preventative_body_scan_message_extra_html' | t: quantity: 4, price_per_scan: '$99', sale_price: '$87'
  assign free_blood_tests_html = 'product.free_blood_tests_html' | t: quantity: 2

  case forloop.index
    when 2
      assign plan_type = '/scan'
      assign plan_label = 'One-time'
      assign plan_features = preventative_body_scan | append: ',' | append: in_person_consultation | split: ','
    when 3
      assign plan_type = '/ yr'
      assign plan_label = 'general.customer.annual_membership' | t
      assign combined_features = preventative_body_scan_message_extra | append: ',' | append: free_blood_tests_html | append: ',' | append: in_person_consultation
      assign plan_features = combined_features | split: ','
  endcase
%}

{% if product.price == 0 %}
  <p class="price-message text-base font-bold text-secondary">
    {{ 'sections.healthpass_offerings.additional_pricing_info' | t }}
  </p>
{% else %}
  <div class="price-block bg-primary-light price-block-index-{{ forloop.index }}">
    <div class="price-text bg-gradient-primary p-3 rounded-xl w-full price-text-index-{{ forloop.index }}">
      <p class="text-[28px] leading-none text-white text-center font-bold">
        {{- product.price | money_without_trailing_zeros -}}
        <span class="text-base">{{- plan_type -}}</span>
      </p>
    </div>

    {% if plan_label != '' %}
      <div class="flex flex-col gap-1.5">
        <p class="font-bold text-primary-gradient">{{ plan_label }}</p>
        <div class="flex flex-col gap-1">
          {% for feature in plan_features %}
            <p class="text-xs text-secondary">{{ feature }}</p>
          {% endfor %}
        </div>
      </div>
    {% endif %}
  </div>
{% endif %}
