{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  .section-inner-wrapper hr {
    border: 0;
    height: 2px;
    background-image: linear-gradient(90deg, rgba(200, 39, 125, 0) 0%, #C8274E 19.205%, #C82763 79.0473%, rgba(200, 39, 125, 0) 100%);
  }

  .how-it-works-item:nth-child(2) img {
    transform: scale(1.32);
    margin-top: 34px;
  }
{%- endstyle -%}

<div class="how-it-works-section section-{{ section.id }}-margin">
  <div class="wrapper size-full py-16" style="background: {{- section.settings.bg_color -}};">
    <div class="heading-block w-full md:w-1/2 text-center mx-auto mb-8 md:mb-16">
      <h1 class="section-heading-40 text-secondary font-bold mb-2">
        {{ section.settings.title }}
      </h1>
      {% if section.settings.paragraph %}
        <div class="mt-3 text-base w-full md:max-w-[590px] mx-auto">{{ section.settings.paragraph }}</div>
      {% endif %}
    </div>
    <div class="section-inner-wrapper inline-flex items-center justify-center w-full relative">
      <hr class="w-full absolute" style="top: 38%;">
      <div class="container how-it-works-items grid grid-cols-1 md:grid-cols-3 gap-7 md:gap-36 justify-stretch relative content-block w-full">
        {%- for block in section.blocks -%}
          <div class="how-it-works-item grid grid-cols-1 justify-between w-fit">
            <div class="image-block mb-6 md:mb-12">
              {% if block.settings.card_image -%}
                <img
                  src="{{ block.settings.card_image | image_url: width: 450 }}"
                  alt="{{ block.settings.card_image.alt }}"
                  width="{{ block.settings.card_image.width }}"
                  height="{{ block.settings.card_image.height }}"
                  class="max-w-[280px] h-auto"
                >
              {% else %}
                <div
                  role="status"
                  class="flex items-center justify-center size-44 max-w-sm bg-gray-2 rounded-lg animate-pulse"
                >
                  {{- 'icon-image-skeleton.svg' | inline_asset_content -}}
                  <span class="sr-only">Loading...</span>
                </div>
              {% endif %}
            </div>
            <div class="text-block mt-auto">
              <div class="content-block text-center">
                <h2 class="text-2xl text-primary-gradient font-bold inline-flex">
                  {{- block.settings.heading -}}
                </h2>
                {% unless block.settings.description == blank %}
                  <div class="text-secondary text-base mt-1">{{- block.settings.description -}}</div>
                {% endunless %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
    {% unless section.settings.btn_text == blank %}
      <div class="container button-block mt-16 flex justify-center">
        <a
          href="{{- section.settings.btn_url -}}"
          target="{{- section.settings.btn_target -}}"
          class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if section.settings.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
        >
          {% if section.settings.btn_variant != 'solid_primary' %}
            <span class="text-primary-gradient text-sm font-bold">
              {{- section.settings.btn_text -}}
            </span>
          {% else %}
            {{- section.settings.btn_text -}}
          {% endif %}
        </a>
      </div>
    {% endunless %}
  </div>
</div>

{% schema %}
{
  "name": "How Works 3rd option",
  "max_blocks": 3,
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Add section title",
      "default": "How it Works"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add the subtext",
      "default": "<p>Using advanced imaging technology and anthropometric science, Healthpass accurately estimates your health risks in seconds without exposing you to unnecessary radiation</p>"
    },
    {
      "type": "color_background",
      "id": "bg_color",
      "label": "Background color"
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Primary button",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Btton URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        { "value": "_blank", "label": "Open in a New Tab" },
        { "value": "_self", "label": "Open in Same Tab" }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 16
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 16
    }
  ],
  "blocks": [
    {
      "type": "how_it_works",
      "name": "how_it_works",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Card Heading",
          "default": "Register your testkits"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Paragraph",
          "default": "<p>Once you have your test, open the Styku mobile app and seamlessly register your test kit."
        },
        {
          "type": "image_picker",
          "id": "card_image",
          "label": "Upload image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "How Works 3rd option",
      "blocks": [
        {
          "type": "how_it_works"
        },
        {
          "type": "how_it_works"
        },
        {
          "type": "how_it_works"
        }
      ]
    }
  ]
}
{% endschema %}
