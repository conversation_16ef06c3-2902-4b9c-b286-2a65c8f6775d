// Function to login into the Shopify
async function shopifyCustomerLogin(email, password) {
  try {
    var requestBody =
      'customer[email]=' +
      encodeURIComponent(email) +
      '&customer[password]=' +
      encodeURIComponent(password);

    const shopifyCustomerLoginApiPayload = API_PAYLOADS.SHOPIFY_CUSTOMER_LOGIN;
    shopifyCustomerLoginApiPayload.body = requestBody;

    const response = await fetchData(shopifyCustomerLoginApiPayload);

    return response;
  } catch (error) {
    console.error('Error during Login into Shopify:', error);
    throw error;
  }
}
