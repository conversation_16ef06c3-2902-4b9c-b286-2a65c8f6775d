{{ 'product.css' | asset_url | stylesheet_tag }}
<div class="container px-4 lg:px-0">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-16 mt-8 md:mt-12">
    <div class="product-image-block">
      <div class="image-wrapper h-auto md:max-h-[428px] md:min-h-[428px] mb-6 overflow-hidden">
        <div
          id="featuredImage"
          class="owl-carousel owl-theme flex justify-center"
        >
          {% render 'product-media',
            product_media: product.media,
            width: 1600,
            crop: 'center',
            class: 'featured-image'
          %}
        </div>
      </div>
      <div class="thumbnail-wrapper w-full md:w-4/5 mx-auto">
        <div id="thumbsImage" class="flex justify-center owl-carousel product-slider owl-theme">
          {% render 'product-media',
            product_media: product.media,
            width: 110,
            height: 80,
            crop: 'center',
            class: 'thumbs-image'
          %}
        </div>
      </div>
    </div>
    <div class="product-details-block">
      <h2 class="heading-level-1 mb-2">{{ product.title }}</h2>
      {% if product.type == 'health-products' %}
        <div class="mb-2">
          <span class="p-2 bg-[#53cc2824] gap-2 rounded-md inline-flex">
            <span class="text-sm text-[#53CC28] font-bold">{{ 'sections.health_product.badge' | t }}</span>
          </span>
        </div>
      {% endif %}

      <div class="mb-6">
        <span class="text-[40px] text-secondary font-bold leading-none mr-2">{{ product.price | money }}</span>
        {%- if product.compare_at_price -%}
          <span class="line-through text-gray-3">{{ product.compare_at_price | money }}</span>
        {%- endif -%}
      </div>
      <div class="product-decrirtion mb-6">
        <p class="text-base font-bold text-secondary mb-2">{{ 'product.description' | t }}</p>
        <div class="text-gray-8 mb-6 text-base">
          {{ product.description }}
        </div>
      </div>
      <div class="product-quantity mb-6 md:mb-8">
        <p class="text-base font-bold text-secondary mb-2">{{ 'product.quantity' | t }}</p>
        <div class="relative flex items-center max-w-[8rem]">
          <button
            type="button"
            disabled
            class="quantity-decrement-button size-7 inline-flex justify-center items-center text-sm font-medium rounded-full bg-[#E6E6E6]"
          >
            {{- 'icon-minus.svg' | inline_asset_content -}}
          </button>
          <input
            type="text"
            name="updates[]"
            id="productQuantityInput"
            value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
            data-input-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
            data-input-max=""
            class="text-base product-quantity-input w-9 p-2 h-7 text-center focus:outline-none"
            readonly
          >
          <button
            type="button"
            class="quantity-increment-button size-7 inline-flex justify-center items-center text-sm font-medium rounded-full bg-[#E6E6E6]"
          >
            {{- 'icon-plus.svg' | inline_asset_content -}}
          </button>
        </div>
      </div>
      {% render 'testkits_badge' %}
      {% render 'product-form', product: product %}
    </div>
  </div>
</div>
<div class="extra-info mt-10 md:mt-28">
  <div class="accordion-wrapper container px-4 lg:px-0">
    <div
      class="accordion px-4 py-5 md:p-16 rounded-2xl border border-gray-2"
      style="box-shadow: 0px 0px 68px 0px rgba(0, 0, 0, 0.05);"
    >
      {% for details in product.metafields.vital.testkit_details.value %}
        <div class="accordion-item border-b border-gray-2 last:border-none mb-6 md:mb-8">
          <button
            aria-expanded="false"
            class="accordion-title-block flex justify-between gap-7 w-full text-left"
          >
            <span class="grow text-base md:text-lg text-secondary font-bold text-left">{{ details.question }}</span>
            <span class="flex-none w-10 icon" aria-hidden="true"></span>
          </button>
          <div class="accordion-body accordion-content w-[95%] mb-6 md:mb-8 mt-2">
            {{ details.answer | metafield_tag }}
          </div>
        </div>
      {% endfor %}
    </div>
  </div>
</div>
<div class="container px-4 lg:px-0">
  {% for block in section.blocks %}
    {% case block.type %}
      {% when 'featured_product' %}
        <div class="flex flex-col md:flex-row gap-6 md:gap-2 items-center mx-auto justify-center max-w-[850px] mt-7 md:mt-20 mb-6 md:mb-8">
          <div class="heading-block text-center">
            <h2 class="section-heading-40 text-secondary">{{- block.settings.title -}}</h2>
          </div>
        </div>
        {% render 'health-product-grid',
          product: block.settings.collection.products,
          classes: 'md:grid-cols-2 lg:grid-cols-3 mb-6'
        %}
      {% when 'testkit_faq' %}
        <div class="testkits-faq w-full text-center mx-auto mt-7 md:mt-24">
          <div class="text-block mb-16">
            <h1 class="section-heading-40 text-secondary font-bold">
              {{ block.settings.title }}
            </h1>
            {% if section.settings.unable_paragraph %}
              <div class="mt-3 text-base">{{ section.settings.paragraph }}</div>
            {% endif %}
          </div>
          <div class="content-block max-w-3xl mx-auto">
            <div class="accordion">
              {% assign sorted_testkit_faqs = shop.metaobjects.testkits_faq.values | sort_natural: 'custom_sort' %}
              {% for testkit_faq in sorted_testkit_faqs %}
                {% if testkit_faq.reference.value.id == product.id %}
                  <div class="accordion-item border-b border-gray-2 last:border-none mb-6 md:mb-8">
                    <button
                      aria-expanded="false"
                      class="accordion-title-block flex justify-between gap-7 w-full text-left"
                    >
                      <span class="grow text-base md:text-lg text-secondary font-medium text-left">
                        {{ testkit_faq.question.value }}
                      </span>
                      <span class="flex-none w-10 icon" aria-hidden="true"></span>
                    </button>
                    <div class="accordion-body accordion-content w-[95%] mb-6 md:mb-8 mt-2">
                      <div class="paragraph-text-responsive text-left">{{ testkit_faq.answer.value }}</div>
                    </div>
                  </div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
        </div>
    {% endcase %}
  {% endfor %}
</div>

{% schema %}
{
  "name": "Product main",
  "settings": [],
  "blocks": [
    {
      "type": "featured_product",
      "name": "Featured Product",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Block Title"
        },
        {
          "type": "collection",
          "id": "collection",
          "label": "Choose collection"
        }
      ]
    },
    {
      "type": "testkit_faq",
      "name": "Testkit FAQ",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Frequently Asked Questions"
        }
      ]
    }
  ]
}
{% endschema %}
