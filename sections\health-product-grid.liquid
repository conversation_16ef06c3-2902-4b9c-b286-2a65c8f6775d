<section>
  <div class="container px-4 lg:px-0">
    <div
      class="flex flex-col md:flex-row gap-6 md:gap-2 justify-between items-center mt-[18px] mb-6 md:mt-9 md:mb-9 w-[94%] md:w-full mx-auto"
    >
      <div class="heading-block text-center md:text-left">
        <h2 class="heading-level-3 mb-1.5">{{ 'sections.health_product.title' | t }}</h2>
        <p class="text-sm md:text-base !text-gray-8">{{ 'sections.health_product.subtitle' | t }}</p>
      </div>
      {% render 'continue-checkout-button' %}
    </div>
    {% render 'health-product-grid', product: collection.products, classes: 'md:grid-cols-2 lg:grid-cols-3 mb-6' %}
    <div class="flex justify-center mb-6 md:mt-9 md:mb-9">
      {% render 'continue-checkout-button' %}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Health Products",
  "class": "section"
}
{% endschema %}
