<div class="container px-4 lg:px-0">
  <div class="main-wrapper mx-auto mb-7 mt-9 md:mb-11 md:mt-11">
    <div class="customer-section flex flex-col md:flex-row items-start gap-6">
      <aside class="sidebar w-full grid grid-cols-1 md:max-w-[345px] top-[132px] md:sticky">
        <div class="navigation-box rounded-2xl border border-gray-2 p-4 md:p-6 ">
          <div class="user-profile block">
            <div class="grid mx-auto">
              <div class="status-block">
                <p class="text-base mb-2">
                  <span class="text-secondary text-base font-bold">{{ 'customer.order.payment_status' | t }}:</span>
                  {% liquid
                    assign styles = ''
                    case order.financial_status_label
                      when 'Paid'
                        assign styles = 'bg-green-100 text-green-800'
                      when 'Refunded', 'Partially refunded'
                        assign styles = 'bg-red-100 text-red-800'
                      else
                        assign styles = 'bg-gray-100 text-secondary'
                    endcase
                  %}
                  <span class="status text-xs font-medium ms-2 px-2 py-0.5 rounded-md select-none {{ styles }}">
                    {{ order.financial_status_label }}
                  </span>
                </p>
                <p class="text-base mb-5">
                  <span class="text-secondary text-base font-bold">{{ 'customer.order.fulfillment_status' | t }}:</span>
                  {% liquid
                    assign styles = ''
                    case order.fulfillment_status_label
                      when 'Fulfilled'
                        assign styles = 'bg-green-100 text-green-800'
                      when 'Unfulfilled'
                        assign styles = 'bg-yellow-100 text-yellow-800'
                      else
                        assign styles = 'bg-gray-100 text-secondary'
                    endcase
                  %}
                  <span class="status text-xs font-medium ms-2 px-2 py-0.5 rounded-md select-none {{ styles }}">
                    {{ order.fulfillment_status_label }}
                  </span>
                </p>
              </div>
              <div class="billing-address-block mb-5">
                <h2 class="text-lg md:text-xl font-bold text-secondary pb-2 border-b">
                  {{ 'customer.order.billing_address' | t }}
                </h2>

                <div class="address-block">{{ order.billing_address | format_address }}</div>
              </div>
              <div class="shipping-address-block pt-2">
                <h2 class="text-lg md:text-xl font-bold text-secondary pb-2 border-b">
                  {{ 'customer.order.shipping_address' | t }}
                </h2>

                {% if order.shipping_address %}
                  <div class="text-base">{{ order.shipping_address | format_address }}</div>
                {% else %}
                  {{ '-' }}
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </aside>
      <div class="main-content w-full">
        <div id="orders" class="orders-block orders">
          <div class="billing-and-payments-informations space-y-6">
            <div class="rounded-2xl border border-gray-2 p-4 md:p-6">
              <div class="header-block mb-6 flex justify-between items-start">
                <div class="title-block">
                  <h3 class="heading-level-3">{{ 'customer.order.title' | t: name: order.name }}</h3>
                  {%- assign order_date = order.created_at | date: '%b %d %Y %H:%M' -%}
                  <p class="text-base text-gray-5">{{ 'customer.order.date_html' | t: date: order_date }}</p>
                  {%- if order.cancelled -%}
                    {%- assign cancelled_at = order.cancelled_at | date: '%b %d %Y %H:%M' -%}
                    <p class="text-base text-gray-5">{{ 'customer.order.cancelled_html' | t: date: cancelled_at }}</p>
                    <p class="text-base text-gray-5">
                      {{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason_label }}
                    </p>
                  {%- endif -%}
                </div>
                <div class="checkout-button flex w-full md:w-auto">
                  <a
                    href="{{ routes.account_url }}"
                    type="button"
                    class="button-primary-gradient-outline text-center rounded-full py-2 px-6 leading-[0] w-full md:w-auto"
                    role="button"
                  >
                    <span class="text-primary-gradient text-sm font-bold">
                      {{ 'customer.account.return' | t }}
                    </span>
                  </a>
                </div>
              </div>

              <div class="customer-orders-block relative overflow-x-auto rounded-lg border border-gray-2">
                <table role="table" class="w-full text-sm text-left text-secondary">
                  <thead role="rowgroup" class="text-sm text-secondary uppercase bg-gray-2">
                    <tr role="row">
                      <th id="ColumnProduct" scope="col" class="p-4" role="columnheader">
                        {{ 'customer.order.product' | t }}
                      </th>
                      <th id="ColumnPrice" scope="col" class="p-4" role="columnheader">
                        {{ 'customer.order.price' | t }}
                      </th>
                      <th id="ColumnQuantity" scope="col" class="p-4" role="columnheader">
                        {{ 'customer.order.quantity' | t }}
                      </th>
                      <th id="ColumnTotal" scope="col" class="p-4" role="columnheader">
                        {{ 'customer.order.total' | t }}
                      </th>
                    </tr>
                  </thead>
                  <tbody role="rowgroup">
                    {%- for line_item in order.line_items -%}
                      <tr role="row" class="bg-white border-b last:border-none hover:bg-[#fafafaea]">
                        <td
                          id="Row{{ line_item.key }}"
                          headers="ColumnProduct"
                          role="rowheader"
                          scope="row"
                          class="p-4"
                          data-label="{{ 'customer.order.product' | t }}"
                        >
                          <div class=" text-secondary">
                            <p class="text-base text-secondary">{{ line_item.title | escape }}</p>
                            {%- assign property_size = line_item.properties | size -%}
                            {%- unless line_item.selling_plan_allocation == null and property_size == 0 -%}
                              <div class="properties">
                                {%- unless line_item.product.has_only_default_variant -%}
                                  <span class="text-base text-secondary">
                                    {{ line_item.variant.title | escape }}
                                  </span>
                                {%- endunless -%}
                                {%- unless line_item.selling_plan_allocation == null -%}
                                  <span class="text-xs text-gray-8">
                                    {{ line_item.selling_plan_allocation.selling_plan.name }}
                                  </span>
                                {%- endunless -%}
                              </div>
                            {%- endunless -%}
                            {%- if line_item.line_level_discount_allocations != blank -%}
                              <ul role="list" aria-label="{{ 'customer.order.discount' | t }}">
                                {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                                  <li class="text-xs text-gray-8">
                                    {{- discount_allocation.discount_application.title | escape }} (-
                                    {{- discount_allocation.amount | money -}}
                                    )
                                  </li>
                                {%- endfor -%}
                              </ul>
                            {%- endif -%}
                            {%- if line_item.fulfillment -%}
                              <div class="fulfillment mt-1">
                                {%- assign created_at = line_item.fulfillment.created_at | date: '%b %d %Y %H:%M' -%}
                                <span class="text-xs font-medium px-2 py-0.5 rounded-md select-none bg-green-100 text-green-800">
                                  {{- 'customer.order.fulfilled_at_html' | t: date: created_at -}}
                                </span>
                              </div>
                            {%- endif -%}
                            {% for line in order.subtotal_line_items %}
                              {% if line_item.id == line.id and line.refunded_quantity > 0 %}
                                {% assign refunded_msg = 'customer.order.total_refunded' | t %}
                                <div class="fulfillment mt-1">
                                  <span class="status text-xs font-medium px-2 py-0.5 rounded-md select-none bg-red-100 text-red-800">
                                    {{ refunded_msg -}}
                                    {%- unless line.refunded_quantity == line_item.quantity -%}
                                      {{- line.refunded_quantity | append: ' item' | prepend: ' ' -}}
                                    {%- endunless %}
                                  </span>
                                </div>
                              {% endif %}
                            {% endfor %}
                          </div>
                        </td>
                        <td
                          class="p-4"
                          headers="Row{{ line_item.key }} ColumnPrice"
                          role="cell"
                          data-label="{{ 'customer.order.price' | t }}"
                        >
                          {%- if line_item.original_price != line_item.final_price
                            or line_item.unit_price_measurement
                          -%}
                            <dl>
                              {%- if line_item.original_price != line_item.final_price -%}
                                <dd class="regular-price">
                                  <s>{{ line_item.original_price | money }}</s>
                                </dd>
                                <dd>
                                  <span>{{ line_item.final_price | money }}</span>
                                </dd>
                              {%- else -%}
                                <dd>
                                  {{ line_item.original_price | money }}
                                </dd>
                              {%- endif -%}
                              {%- if line_item.unit_price_measurement -%}
                                <dd class="unit-price">
                                  <span>
                                    {%- capture unit_price_separator -%}
                              <span aria-hidden="true">/</span>
                              {%- endcapture -%}
                                    {%- capture unit_price_base_unit -%}
                              {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                              {{- line_item.unit_price_measurement.reference_value -}}
                              {%- endif -%}
                              {{ line_item.unit_price_measurement.reference_unit }}
                              {%- endcapture -%}
                                    <span data-unit-price>{{ line_item.unit_price | money }}</span>
                                    {{- unit_price_separator -}}
                                    {{- unit_price_base_unit -}}
                                  </span>
                                </dd>
                              {%- endif -%}
                            </dl>
                          {%- else -%}
                            <span>{{ line_item.final_price | money }}</span>
                          {%- endif -%}
                        </td>
                        <td
                          class="p-4"
                          headers="Row{{ line_item.key }} ColumnQuantity"
                          role="cell"
                          data-label="{{ 'customer.order.quantity' | t }}"
                        >
                          {{ line_item.quantity }}
                        </td>
                        <td
                          class="p-4"
                          headers="Row{{ line_item.key }} ColumnTotal"
                          role="cell"
                          data-label="{{ 'customer.order.total' | t }}"
                        >
                          {%- if line_item.original_line_price != line_item.final_line_price -%}
                            <dl>
                              <dd class="regular-price">
                                <s>{{ line_item.original_line_price | money }}</s>
                              </dd>
                              <dd>
                                <span>{{ line_item.final_line_price | money }}</span>
                              </dd>
                            </dl>
                          {%- else -%}
                            <p>{{ line_item.original_line_price | money }}</p>
                            {% for line in order.subtotal_line_items %}
                              {% if line_item.id == line.id and line.refunded_quantity > 0 %}
                                {% assign refunded_amount = line.final_price | times: line.refunded_quantity %}
                                {% assign price_after_refunded = line_item.final_line_price | minus: refunded_amount %}
                                {% if price_after_refunded != line.final_price and price_after_refunded > 0 %}
                                  <p class="price-after-refunded mt-1 text-xs">
                                    {{- price_after_refunded | money | prepend: '-' -}}
                                  </p>
                                {% endif %}
                              {% endif %}
                            {% endfor %}
                          {%- endif -%}
                        </td>
                      </tr>
                    {%- endfor -%}
                  </tbody>
                </table>
              </div>
              <div class="flex flex-col px-4 py-4 md:p-4 w-full bg-gray-50 space-y-6 mt-4 rounded-lg">
                <h3 class="text-xl  font-semibold leading-5 text-secondary">Summary</h3>
                <div class="flex justify-center items-center w-full space-y-4 flex-col border-gray-200 border-b pb-4">
                  <div class="flex justify-between w-full text-base font-bold">
                    <p class="text-secondary">{{ 'customer.order.subtotal' | t }}</p>
                    <p class="text-secondary">{{ order.line_items_subtotal_price | money }}</p>
                  </div>
                  {%- if order.cart_level_discount_applications != blank -%}
                    <div class="flex justify-between items-center w-full">
                      {%- for discount_application in order.cart_level_discount_applications -%}
                        <p class="text-base  leading-4 text-secondary">
                          {{ 'customer.order.discount' | t }}
                          <span
                            class="bg-gray-200 p-1 text-xs font-medium leading-3 text-secondary"
                          >
                            {{- discount_application.title | escape -}}
                          </span>
                        </p>
                        <td headers="RowDiscount" role="cell" data-label="{{ 'customer.order.discount' | t }}">
                          <div>
                            <span>-{{ discount_application.total_allocated_amount | money }}</span>
                            <span class="cart-discount">
                              {{- discount_application.title | escape -}}
                            </span>
                          </div>
                        </td>
                      {%- endfor -%}
                    </div>
                  {%- endif -%}
                  {%- for tax_line in order.tax_lines -%}
                    <div class="flex justify-between items-center w-full">
                      <p class="text-base leading-4 text-secondary">
                        {{ 'customer.order.tax' | t }} ({{ tax_line.title | escape }}
                        {{ tax_line.rate | times: 100 }}%)
                      </p>
                      <p class="text-base leading-4 text-secondary">
                        {{ tax_line.price | money }}
                      </p>
                    </div>
                  {%- endfor -%}
                  {%- if order.total_refunded_amount > 0 -%}
                    <div class="flex justify-between items-center w-full">
                      <p class="text-base leading-4 text-secondary">
                        {{ 'customer.order.total_refunded' | t }}
                      </p>
                      <p class="text-base leading-4 text-secondary">
                        {{ order.total_refunded_amount | money | prepend: '-' }}
                      </p>
                    </div>
                  {%- endif -%}
                  {%- for shipping_method in order.shipping_methods -%}
                    <div class="flex justify-between items-center w-full">
                      <p class="text-base leading-4 text-secondary">
                        {{ 'customer.order.shipping' | t }} ({{ shipping_method.title | escape }})
                      </p>
                      <p class="text-base leading-4 text-gray-8">{{ shipping_method.price | money }}</p>
                    </div>
                  {%- endfor -%}
                </div>
                <div class="flex justify-between items-center w-full text-base font-bold">
                  <p class="text-secondary">{{ 'customer.order.total' | t }}</p>
                  <p class="text-secondary">{{ order.total_net_amount | money }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
