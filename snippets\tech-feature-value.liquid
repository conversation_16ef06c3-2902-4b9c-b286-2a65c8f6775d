{% liquid
  assign yes_string = 'Yes'
  assign no_string = 'No'
%}

{% case feature_value %}
  {% when yes_string %}
    <div class="flex {{ class -}}">
      <img
        src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/primary-gradient-check-mark-icon.svg?v=1735109780"
        alt="Yes"
        width="24"
        loading="eager"
        height="24"
      >
    </div>
  {% when no_string %}
    <div class="flex {{ class -}}">
      <img
        src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/secondry-cross-icon.svg?v=1735109781"
        alt="No"
        width="24"
        loading="eager"
        height="24"
      >
    </div>
  {% else %}
    {%- if feature_value contains yes_string %}
      <div class="feature-value h-6 relative {{ class -}}">
        <div class="absolute flex left-[45%] gap-1">
          <img
            src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/primary-gradient-check-mark-icon.svg?v=1735109780"
            alt="Yes"
            width="24"
            loading="eager"
            height="24"
          >
          <span>{{- feature_value | replace: yes_string, '' | strip -}}</span>
        </div>
      </div>
    {% else %}
      {{- feature_value | default: '-' -}}
    {% endif %}
{% endcase %}
