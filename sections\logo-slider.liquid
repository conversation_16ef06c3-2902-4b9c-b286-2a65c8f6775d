{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="partner-logo-wrapper section-{{ section.id }}-margin">
  {% render 'logo-slider',
    settings: section.settings,
    heading: section.settings.heading,
    metaobject_type: shop.metaobjects[section.settings.metaobject_type]
  %}
</div>

{% schema %}
{
  "name": "Logo slider",
  "class": "overflow-hidden w-full",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Trusted by"
    },
    {
      "type": "header",
      "content": "Filter image"
    },
    {
      "type": "range",
      "id": "filter_gray",
      "label": "Manage gray scale",
      "min": 0,
      "max": 1,
      "step": 0.1,
      "default": 0
    },
    {
      "type": "range",
      "id": "filter_opacity",
      "label": "Manage opacity",
      "min": 0,
      "max": 1,
      "step": 0.1,
      "default": 1
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "text",
      "id": "metaobject_type",
      "default": "partner_logo",
      "label": "Metaobject Type",
      "info": "Enter the metaobject type for logo"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Logo slider"
    }
  ]
}
{% endschema %}
