{{ 'pricing.css' | asset_url | stylesheet_tag }}

{% liquid
  assign sorted_product_features = shop.metaobjects[section.settings.metaobject_type].values | sort_natural: 'sort_alphabetically'
  assign show_rows = section.settings.show_rows
  assign buy_now_string = 'product.buy_now' | t
%}
{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  table tbody tr:last-child td:nth-child(2),
  table tbody tr:nth-child({{ show_rows }}) td:nth-child(2) {
    border-radius: 0 0 24px 24px;
    border-right: 1px solid var(--color-gray-2);
    border-bottom: 1px solid var(--color-gray-2);
    border-left: 1px solid var(--color-gray-2);
    background: var(--color-white);
  }

  table tbody tr:last-child td:nth-child(3),
  table tbody tr:nth-child({{ show_rows }}) td:nth-child(3) {
    border-radius: 0 0 24px 24px;
    border-right: 4px solid #81567E;
    border-bottom: 4px solid #81567E;
    border-left: 4px solid #81567E;
    background: #DBC9DA;
  }

  table tbody tr:last-child td:nth-child(4),
  table tbody tr:nth-child({{ show_rows }}) td:nth-child(4) {
    border-radius: 0 0 24px 24px;
    border-bottom: 4px solid var(--color-white);
    background: #E7EDED;
  }

  table tbody tr:last-child td:nth-child(2) .horizontal-line,
  table tbody tr:nth-child({{ show_rows }}) td:nth-child(2) .horizontal-line,
  table tbody tr:last-child td:nth-child(4) .horizontal-line,
  table tbody tr:nth-child({{ show_rows }}) td:nth-child(4) .horizontal-line,
  table tbody tr:last-child td:nth-child(3) .horizontal-line,
  table tbody tr:nth-child({{ show_rows }}) td:nth-child(3) .horizontal-line {
    display: none;
  }
{%- endstyle -%}

<section class="py-7 md:py-11 section-{{ section.id }}-margin">
  <div class="container px-4 lg:px-0">
    <div class="heading-block text-center md:w-full mx-auto">
      <h2 class="section-heading-40 mb-2">{{ 'sections.pricing.title' | t }}</h2>
      <p class="paragraph-text-responsive">{{ 'sections.pricing.subtitle' | t }}</p>
    </div>
    <div class="pricing-main-wrapper relative mx-auto md:mt-12 mb-12 md:mb-0 hidden md:block">
      <div class="pricing-table-wrapper">
        <table class="text-left">
          <thead>
            <tr class="table-head-row">
              <td class="pricing-table-header"></td>
              <td class="pricing-table-header"></td>
              <td class="pricing-table-header">
                <p class="most-popular text-lg font-bold text-center">
                  {{ 'sections.healthpass_offerings.most_popular' | t }}
                </p>
              </td>
              <td class="pricing-table-header"></td>
            </tr>
            <tr class="table-head-row">
              <th class="pricing-table-header">
                <div class="text-[34px] leading-tight text-secondary font-bold">
                  {{- section.settings.card_title -}}
                </div>
              </th>
              {% for product in section.settings.products %}
                <th class="pricing-table-header text-base font-bold">
                  <div class="inner-block ">
                    <p class="title">{{- product.title -}}</p>
                    {% if product.price == 0 %}
                      <p class="info-text text-base font-bold text-secondary">
                        {{ 'sections.healthpass_offerings.additional_pricing_info' | t }}
                      </p>
                    {% else %}
                      <div class="price-with-buy-now-block">
                        <div class="price-block mb-4">
                          <span class="price text-4xl md:text-4xl lg:text-[52px] lg:leading-[1.1] font-bold">
                            {{ product.price | money_without_trailing_zeros }}
                          </span>
                        </div>
                        <div class="buy-now-button-block">
                          {% render 'product-form', product: product, button_text: buy_now_string %}
                        </div>
                      </div>
                    {% endif %}
                  </div>
                </th>
              {% endfor %}
            </tr>
          </thead>
          <tbody data-show-rows="{{ show_rows }}">
            {% for sorted_product_feature in sorted_product_features %}
              <tr class="{% if forloop.index > show_rows %}toggle-rows hidden{% endif %}">
                <td class="py-4 font-bold flex gap-1 items-center">
                  <p class="text-base text-secondary" style="display: ruby;">
                    {{- sorted_product_feature.title }}
                    <span class="tooltip_wrapper">
                      <span class="flex-none size-4 cursor-pointer" role="button">
                        {{- 'tooltip-icon.svg' | inline_asset_content -}}
                      </span>
                      <span class="tooltip text-sm font-normal">{{- sorted_product_feature.tooltip_content -}}</span>
                    </span>
                  </p>
                </td>
                {% for product in section.settings.products %}
                  {% liquid
                    assign found = false
                    for referenced_product_id in sorted_product_feature.product_reference.value
                      if referenced_product_id.id == product.id
                        assign found = true
                        break
                      endif
                    endfor
                  %}
                  <td class="text-base py-2 pricing-table-body-content">
                    <div class="inner-block">
                      {% if found == true %}
                        {{- 'checkmark-icon.svg' | inline_asset_content -}}
                      {% else %}
                        {{- 'cross-icon.svg' | inline_asset_content -}}
                      {% endif %}
                      <div class="horizontal-line"></div>
                    </div>
                  </td>
                {% endfor %}
              </tr>
            {% endfor %}
          </tbody>
        </table>
        <div class="see-more-button-block mt-10">
          {% render 'see-more-toggle' %}
        </div>
      </div>
    </div>
    <div class="pricing-card-wrapper mt-9 md:hidden">
      <div class="product-lists w-full grid grid-cols-1 gap-6">
        {% for product in section.settings.products %}
          <div class="product-list">
            {% if forloop.index == 2 %}
              <div class="p-1 bg-[#81567E] rounded-3xl md:-mt-12 min-w-[92%] md:w-full">
                <div class="most-polpular-block my-2 mb-2 flex justify-center">
                  <p class="text-lg font-bold text-[#DBC9DA]">{{ 'sections.healthpass_offerings.most_popular' | t }}</p>
                </div>
            {% endif %}
            <div class="product-card w-full flex flex-col p-6">
              <div class="inner-block">
                <p class="title text-lg font-bold">{{- product.title -}}</p>
                {% if product.price == 0 %}
                  <p class="info-text text-base font-bold text-secondary">
                    {{ 'sections.healthpass_offerings.additional_pricing_info' | t }}
                  </p>
                  <div class="horizontal-line"></div>
                {% else %}
                  <div class="price-with-buy-now-block">
                    <div class="price-block mb-4">
                      <p class="price text-[52px] leading-[1.1] font-bold">
                        {{ product.price | money_without_trailing_zeros }}
                      </p>
                    </div>
                    <div class="buy-now-button-block">
                      {% render 'product-form', product: product, button_text: buy_now_string %}
                    </div>
                  </div>
                  <div class="horizontal-line"></div>
                {% endif %}
              </div>
              <ul class="space-y-4" data-show-rows="{{ show_rows }}">
                {% for sorted_product_feature in sorted_product_features %}
                  {% liquid
                    assign found = false
                    for referenced_product_id in sorted_product_feature.product_reference.value
                      if referenced_product_id.id == product.id
                        assign found = true
                        break
                      endif
                    endfor
                  %}
                  <li class="features-list-value flex gap-3 justify-between items-start {% if forloop.index > show_rows %}toggle-rows hidden{% endif %}">
                    <p class="features-value-title">
                      {{- sorted_product_feature.title }}
                      <span class="tooltip_wrapper">
                        <span class="flex-none size-4 cursor-pointer" role="button">
                          {{- 'tooltip-icon.svg' | inline_asset_content -}}
                        </span>
                        <span class="tooltip text-sm font-normal">{{- sorted_product_feature.tooltip_content -}}</span>
                      </span>
                    </p>
                    <div class="feature-value">
                      {% if found == true %}
                        {{- 'checkmark-icon.svg' | inline_asset_content -}}
                      {% else %}
                        {{- 'cross-icon.svg' | inline_asset_content -}}
                      {% endif %}
                    </div>
                  </li>
                {% endfor %}
              </ul>
            </div>
            {% if forloop.index == 2 %}</div>{% endif %}
            <div class="see-more-button-block mt-2">
              {% render 'see-more-toggle' %}
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Pricing",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 32
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "html",
      "id": "title",
      "label": "Title",
      "default": "Healthpass Offerings"
    },
    {
      "type": "html",
      "id": "card_title",
      "label": "Title",
      "default": "<span class=\"text-primary-gradient\">Healthpass</span> offerings"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description (optional)"
    },
    {
      "type": "product_list",
      "id": "products",
      "label": "Products"
    },
    {
      "type": "text",
      "default": "product_features",
      "id": "metaobject_type",
      "label": "Metaobject Type",
      "info": "Enter the metaobject type for Product feature data"
    },
    {
      "type": "range",
      "id": "show_rows",
      "label": "Number of list to Display",
      "min": 4,
      "max": 30,
      "step": 1,
      "default": 12
    }
  ]
}
{% endschema %}
