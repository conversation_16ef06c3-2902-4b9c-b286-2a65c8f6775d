<div class="container px-4 lg:px-0">
  <div class="flex flex-col md:flex-row gap-6 md:gap-2 items-center mx-auto justify-center max-w-[850px] mt-7 md:mt-20 mb-6 md:mb-8">
    <div class="heading-block text-center">
      <h2 class="section-heading-40 text-secondary">{{- section.settings.heading -}}</h2>
      {% if section.settings.description %}
        <p class="paragraph-text-responsive">{{- section.settings.description -}}</p>
      {% endif %}
    </div>
  </div>
  {% render 'health-product-grid',
    product: section.settings.collection.products,
    classes: 'md:grid-cols-2 lg:grid-cols-3 mb-6'
  %}
</div>

{% schema %}
{
  "name": "Featured collection",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "label": "Heading",
      "id": "heading",
      "default": "Heading"
    },
    {
      "type": "text",
      "label": "Description text",
      "id": "description"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Choose collections"
    }
  ],
  "blocks": [],
  "presets": [
    {
      "name": "Featured collection"
    }
  ]
}
{% endschema %}
