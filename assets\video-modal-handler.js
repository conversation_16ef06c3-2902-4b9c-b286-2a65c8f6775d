class VideoButton extends HTMLElement {
  constructor() {
    super();
    this.addEventListener('click', this.handleClick);
  }

  connectedCallback() {
    this.style.cursor = 'pointer';
  }

  handleClick(event) {
    const videoUrl = this.dataset.url;
    if (videoUrl) {
      createIframeWithWrapper(videoUrl);
      document.body.classList.add('modal-video-active');
    }
  }
}

// Define the custom element
customElements.define('video-button', VideoButton);

// Utility functions (same as in your original script)
function createIframeWithWrapper(videoSrc) {
  const container = createContainer();
  const iframe = createIframe(videoSrc);
  const closeButton = createCloseModalButton();

  container.appendChild(closeButton);
  container.appendChild(iframe);
  document.body.appendChild(container);
}

function createContainer() {
  const container = document.createElement('div');
  container.id = 'modal-video-container';
  container.classList.add('active');
  return container;
}

function createIframe(videoSrc) {
  const cleanUrl = videoSrc.split('?')[0];
  if (
    cleanUrl.endsWith('.mp4') ||
    cleanUrl.endsWith('.mov') ||
    cleanUrl.endsWith('.mkv') ||
    cleanUrl.endsWith('.webm')
  ) {
    const video = document.createElement('video');
    video.src = `${videoSrc}`;
    video.setAttribute('controls', '');
    video.setAttribute('autoplay', '');
    video.setAttribute('muted', '0');
    video.setAttribute('loop', '0');
    return video;
  } else {
    const iframe = document.createElement('iframe');
    iframe.src = `${videoSrc}?rel=0&autoplay=1&mute=0&loop=1`;
    iframe.setAttribute('allow', 'accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture');
    iframe.setAttribute('frameborder', '0');
    iframe.setAttribute('allowfullscreen', '');
    iframe.setAttribute('loading', 'lazy');
    return iframe;
  }
}

function createCloseModalButton() {
  const button = document.createElement('button');
  button.setAttribute('role', 'button');
  button.id = 'closeModalVideo';
  button.innerHTML =
    '<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.46967 5.46967c.29289-.29289.76777-.29289 1.06066 0L12 10.9393l5.4697-5.46963c.2929-.29289.7677-.29289 1.0606 0 .2929.29289.2929.76777 0 1.06066L13.0607 12l5.4696 5.4697c.2929.2929.2929.7677 0 1.0606-.2929.2929-.7677.2929-1.0606 0L12 13.0607l-5.46967 5.4696c-.29289.2929-.76777.2929-1.06066 0s-.29289-.7677 0-1.0606L10.9393 12 5.46967 6.53033c-.29289-.29289-.29289-.76777 0-1.06066Z" fill="#fff"/></svg>';

  button.addEventListener('click', function () {
    document.body.classList.remove('modal-video-active');
    const container = document.getElementById('modal-video-container');
    if (container) {
      container.remove();
    }
  });

  return button;
}
