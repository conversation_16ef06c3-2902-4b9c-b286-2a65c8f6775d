async function profileLogin(email, password) {
  const body = {
    email: email,
    password: password,
  };
  try {
    const loginPayload = API_PAYLOADS.LOGIN;
    loginPayload.body = JSON.stringify(body);

    const response = await fetchData(loginPayload);

    if (response.ok) {
      const responseData = await response.json();

      switch (responseData.status) {
        case SUCCESS_CODE.SUCCESS:
          setLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, responseData.data.accessToken);
          break;
        default:
          showToastMessage(exceptionMessage.somethingWentWrong);
          break;
      }
    } else {
      showToastMessage(exceptionMessage.somethingWentWrong);
    }
  } catch (error) {
    console.error('Error while login:', error);
    showToastMessage(exceptionMessage.somethingWentWrong);
  }
}

// Function to get profile
async function getProfile(accessToken) {
  toggleFullScreenPreloader(true);
  const exceptionMessage = window.errorMessage;
  try {
    const response = await fetchData(API_PAYLOADS.GET_PROFILE(accessToken));
    if (response.ok) {
      const responseData = await response.json();
      setLocalStorageItem(LOCAL_STORAGE_KEY.LOGGED_IN_USER, responseData.data);
      return responseData.data;
    } else {
      showToastMessage(exceptionMessage.somethingWentWrong);
    }
  } catch (error) {
    console.error('Error while fetching profile data:', error);
    showToastMessage(exceptionMessage.somethingWentWrong);
  } finally{
    toggleFullScreenPreloader(false);
  }
}

async function sendVerificationCode(event, targetedButton, email) {
  try {
    toggleFullScreenPreloader(true);

    const successMessage = window.successMessage;
    const exceptionMessage = window.errorMessage;

    event.preventDefault();
    targetedButton.disabled = true;

    const requestBody = {
      email: email,
    };

    const resendVerificationCodePayload = API_PAYLOADS.RESEND_EMAIL_VERIFICATION_CODE;
    resendVerificationCodePayload.body = JSON.stringify(requestBody);

    const response = await fetchData(resendVerificationCodePayload);

    if (response.ok) {
      const responseData = await response.json();

      switch (responseData.status) {
        case SUCCESS_CODE.SUCCESS:
          showToastMessage(successMessage.verificationCodeSent);
          break;
        case ERROR_CODES.UNABLE_TO_SEND_EMAIL:
          showToastMessage(exceptionMessage.unableToSendEmail);
          break;
        default:
          showToastMessage(exceptionMessage.somethingWentWrong);
          break;
      }
    } else {
      showToastMessage(exceptionMessage.somethingWentWrong);
    }
  } catch (error) {
    console.error('Error while sending verification code:', error);
    showToastMessage(exceptionMessage.somethingWentWrong);
  } finally {
    toggleFullScreenPreloader(false);
  }
}
