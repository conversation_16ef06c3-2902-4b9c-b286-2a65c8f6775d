{% liquid
  assign scan_counts = customer.metafields.styku.available_scan_bundles.value

  if customer.metafields.appstle_subscription.subscriptions.value and customer.metafields.appstle_subscription.subscriptions.value.size > 0
    case customer.metafields.appstle_subscription.subscriptions.value[0].status
      when 'active'
        assign is_subscription_active = true
      when 'cancelled'
        assign is_subscription_cancelled = true
      when 'expired'
        assign is_subscription_expired = true
      when 'failed'
        assign is_subscription_failed = true
    endcase
  endif

  assign has_health_pass_in_cart = false
  for cart_item in cart.items
    if cart_item.product.type == 'health-pass'
      assign has_health_pass_in_cart = true
      break
    endif
  endfor
%}

{% render 'cart-items',
  cart_popup: cart_popup,
  sidebar_item: sidebar_item,
  list_wrapper_classes: list_wrapper_classes,
  cart_items: cart.items,
  title_classes: title_classes,
  scan_counts: scan_counts,
  is_subscription_active: is_subscription_active,
  is_subscription_cancelled: is_subscription_cancelled,
  is_subscription_expired: is_subscription_expired,
  is_subscription_failed: is_subscription_failed,
  has_health_pass_in_cart: has_health_pass_in_cart
%}
