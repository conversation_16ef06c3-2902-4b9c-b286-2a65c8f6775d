{%- style -%}
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
      margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-margin {
        margin-top: {{ section.settings.margin_top }}px;
        margin-bottom: {{ section.settings.margin_bottom }}px;
      }
    }

    .icon-block-right-top {
    right: -90px;
    top: -14px;
    z-index: 0;
    position: absolute;
  }

  .rectangle-with-radius {
    width: 471px;
    height: 258px;
    border-radius: 120px 0px 0 120.211px;
    opacity: 0.4;
    background: #c2d3d2;
  }

  .icon-block-right-top .half-circle {
    position: absolute;
    left: 128px;
    bottom: -40px;
    width: 76px;
    height: 158px;
    background: #f84642;
    opacity: 0.4;
    clip-path: path(
      'M0.945318 157.723C0.662734 113.794 0.26712 20.7571 0.94532 0.0410147C98.9766 0.0410159 101.443 157.723 0.945318 157.723Z'
    );
  }

  .icon-block-left-bottom .rectangle {
    width: 120px;
    height: 155.925px;
    opacity: 0.4;
    background: #dbc9da;
    position: absolute;
    bottom: 100px;
    z-index: 2;
  }

  .icon-block-left-bottom .half-circle {
    position: absolute;
    bottom: 0;
    width: 154px;
    height: 138px;
    background: #eea8ba;
    opacity: 0.4;
    clip-path: path('M0 0.919556C81.9583 -9.48221 142.625 70.3885 154 138H0V0.919556Z');
  }

  /* Small devices (phones, 600px and down) */
  @media only screen and (max-width: 768px) {
    .icon-block-right-top {
      right: -170px;
      top: -65px;
    }

    .rectangle-with-radius {
      width: 231px;
      height: 126.545px;
    }

    .icon-block-right-top .half-circle {
      left: 40px;
      bottom: -112px;
      width: 76px;
      height: 158px;
      clip-path: path(
        'M0.25917 77.6218C0.120578 56.0769 -0.0734521 10.4472 0.259171 0.287109C48.3382 0.28711 49.5477 77.6218 0.25917 77.6218Z'
      );
    }

    .icon-block-left-bottom {
      position: absolute;
      bottom: -95px;
    }

    .icon-block-left-bottom .rectangle {
      width: 20px;
      height: 80px;
      opacity: 0.4;
      background: #dbc9da;
      position: absolute;
      bottom: 100px;
      z-index: 2;
    }

    .icon-block-left-bottom .half-circle {
      left: 0px;
      bottom: -20px;
      clip-path: path('M-31.0313 1.31107C2.26428 -2.91465 26.9101 29.5329 31.5312 57.0001H-31.0313V1.31107Z');
    }
  }
{%- endstyle -%}

<div
  class="section-wrapper section-{{ section.id }}-margin"
  style="background:{{ section.settings.bg_color }}"
>
  <div class="partner-banner-block relative">
    <div class="icon-block-right-top">
      <div class="rectangle-with-radius"></div>
      <div class="half-circle"></div>
    </div>
    <div class="icon-block-left-bottom">
      <div class="rectangle"></div>
      <div class="half-circle"></div>
    </div>
    <div class="container px-4 lg:px-0 py-16 md:py-0 md:min-h-[485px] grid justify-center items-center text-center">
      <div class="text-block w-full md:w-[650px]">
        <div class="text-xl font-bold text-primary-gradient mb-4">
          {{- section.settings.sub_text -}}
        </div>
        <div class="text-[28px] leading-8 md:text-[40px] md:leading-[46px] font-bold text-secondary mb-5">
          {{- section.settings.heading -}}
        </div>
        <div class="paragraph-text w-full">
          {{- section.settings.paragraph -}}
        </div>
        <div class="btn-block mt-8 inline-grid">
          {% render 'watch-now-button',
            btn_text: section.settings.button_label,
            shopify_hosted_video_link: section.settings.shopify_hosted_video,
            external_video_link: section.settings.external_video,
            video_btn_variant: section.settings.video_btn_variant
          %}
        </div>
      </div>
    </div>
  </div>

  {% liquid
    if section.settings.partner_logo
      assign trusted_by = 'general.trusted_by' | t
      render 'logo-slider', heading: trusted_by, metaobject_type: shop.metaobjects.trusted_by
    endif
  %}
</div>

{% schema %}
{
  "name": "Science",
  "class": "overflow-hidden w-full",
  "settings": [
    {
      "type": "checkbox",
      "id": "partner_logo",
      "label": "Show partner log",
      "default": true
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Trusted, Science-Backed Whole Body Scanning"
    },
    {
      "type": "richtext",
      "id": "sub_text",
      "label": "Subtext",
      "default": "<p>Discover the science behind Styku</p>"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Heading",
      "default": "<p>Accurate, safe, and trusted 3D whole body scanning technology validated by research and back by scientific professionals.</p>"
    },
    {
      "type": "color_background",
      "id": "bg_color",
      "label": "Background color"
    },
    {
      "type": "checkbox",
      "id": "float_icon",
      "label": "Show and Hide the Floating Icon",
      "default": true
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Label text for botton",
      "default": "Click me"
    },
    {
      "type": "header",
      "content": "Video settings",
      "info": "[Note:] Use only one of the options. When both External and Shopify-hosted are added, the Shopify-hosted option will be used."
    },
    {
      "type": "video_url",
      "id": "external_video",
      "label": "External video",
      "accept": ["youtube", "vimeo"],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
    },
    {
      "type": "video",
      "id": "shopify_hosted_video",
      "label": "Shopify-hosted video"
    },
    {
      "type": "select",
      "id": "video_btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    }
  ]
}
{% endschema %}
