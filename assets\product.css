.product-slider .owl-nav .owl-prev {
  left: -30px;
  position: relative;
}

.product-slider .owl-nav .owl-next {
  right: -30px;
  position: relative;
}

.product-slider .owl-nav {
  margin-top: 0;
  display: flex;
  text-align: justify;
  justify-content: space-between;
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  z-index: -1;
}

#thumbsImage .owl-item {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--color-gray-1);
  height: 85px !important;
  border-radius: 10px;
  background: var(--color-gray-1);
  overflow: hidden;
  cursor: pointer;
}

#featuredImage .owl-item {
  border-radius: 16px;
  background: var(--color-gray-1);
  overflow: hidden;
}

.owl-item.current {
  border: 1px solid #eb3b72 !important;
}
.product-slider .owl-nav [class*='owl-']:hover {
  background: none;
  color: inherit;
  text-decoration: none;
}
.product-slider .owl-nav [class*='owl-'] {
  margin: 0;
  padding: 0;
  background: none;
  display: inline-block;
  cursor: pointer;
  border-radius: 4px;
}

.accordion-body .metafield-rich_text_field {
  margin-top: 24px;
}

.accordion-body .metafield-rich_text_field p {
  color: var(--color-gray-8);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  margin-bottom: 16px;
}

.accordion-body .metafield-rich_text_field p:last-child {
  margin-bottom: 0px;
}

.accordion-body .metafield-rich_text_field p strong {
  color: var(--color-secondary);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 22px;
}

.accordion-body .metafield-rich_text_field p:has(> strong:only-child) {
  margin-bottom: 8px;
}

/* Small devices (phones, 600px and down) */
@media only screen and (max-width: 768px) {
  .product-slider .owl-nav {
    display: none;
  }
  #thumbsImage .owl-item {
    max-height: 85px !important;
    height: 100% !important;
  }
}

.btn-atc.button-primary-light {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
