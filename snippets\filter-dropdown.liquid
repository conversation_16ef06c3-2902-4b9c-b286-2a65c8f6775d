<div
  class="relative w-full filter-dropdown"
  id="{{ dropdown_id }}"
  data-label-key="{{ label_translation_key }}"
  data-metaobject-namespace="{{ metaobject_namespace }}"
>
  <button
    id="{{ dropdown_id }}Toggle"
    class="flex w-full items-center justify-between rounded-md bg-white px-4 py-[10px] text-base text-gray-5 border border-gray-2 cursor-pointer select-none lg:max-w-[350px]"
    aria-haspopup="listbox"
    aria-expanded="false"
  >
    <span
      id="{{ dropdown_id }}SelectedLabel"
      class="overflow-hidden whitespace-nowrap text-ellipsis text-gray-5"
    >
      {{- label_translation_key | t -}}
    </span>
    {% render 'chevron-down-icon' %}
  </button>

  <div
    id="{{ dropdown_id }}Options"
    class="filter-options absolute mt-1 w-full rounded-md bg-white py-[10px] ps-3 pr-[7px] ring-1 ring-gray-300"
    style="display: none;"
    role="listbox"
    aria-labelledby="{{- dropdown_id -}}Toggle"
  >
    {% if include_default_option %}
      <li
        class="text-gray-800 link-item cursor-pointer select-none py-1 text-base"
        data-value="all"
        role="option"
      >
        {{- label_translation_key | t -}}
      </li>
    {% endif %}

    {% render 'dropdown-options',
      metaobject_namespace: metaobject_namespace,
      value_key: value_key,
      title_key: title_key,
      item_class: item_class
    %}
  </div>
</div>
