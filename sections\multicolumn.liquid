{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  .multicolumns::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
{%- endstyle -%}

<div class="multicolumn-wrapper section-{{ section.id }}-margin {{ section.settings.desktop_section_padding_top }} {{ section.settings.desktop_section_padding_bottom }} {{ section.settings.mobile_section_padding_top }} {{ section.settings.mobile_section_padding_bottom }}">
  <div class="container px-4 md:px-0">
    {% unless section.settings.section_heading == blank %}
      <div class="flex flex-col mx-auto justify-start md:justify-center w-full md:w-[85%]  lg:text-left">
        <h1 class="heading-level-2 text-secondary text-center">
          {{ section.settings.section_heading }}
        </h1>
        {% unless section.settings.paragraph == blank %}
          <div class="mt-2 text-base text-center">
            {{ section.settings.paragraph }}
          </div>
        {% endunless %}
      </div>
    {% endunless %}
    <div class="mt-8 md:my-12 multicolumns w-full flex md:grid md:grid-cols-4 overflow-x-auto gap-8 snap-x md:snap-none snap-mandatory">
      {%- for block in section.blocks -%}
        <div class="relative overflow-hidden rounded-lg min-h-80 grid grid-cols-1 content-stretch border border-gray-2">
          {%- if block.settings.image != blank -%}
            <img
              class="object-cover w-full h-[365px] md:h-[365px] lazy-load blur-loading"
              width=""
              height=""
              alt="{{ block.settings.image.alt }}"
              src="{{ block.settings.image | image_url: width: 800 }}"
            >
          {%- else %}
            {{ 'image' | placeholder_svg_tag: 'w-[260px] object-contain' }}
          {% endif %}
          <div class="absolute inset-x-0 bottom-6 px-4">
            <div class="text-xl text-white font-bold">{{ block.settings.title }}</div>
          </div>
        </div>
      {% endfor %}
    </div>
    {% unless section.settings.btn_primary_text == blank %}
      <div class="button-block flex justify-center">
        <a
          class="flex justify-center cursor-pointer items-center gap-4 !text-base !py-3 !px-9 button-primary-gradient"
          href="{{ section.settings.btn_primary_url }}"
          target="{{ section.settings.btn_primary_target }}"
        >
          {{ section.settings.btn_primary_text }}
        </a>
      </div>
    {% endunless %}
  </div>
</div>
{% schema %}
{
  "name": "Multicolumn",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "text",
      "id": "section_heading",
      "label": "Section heading",
      "default": "Research Studies"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add description"
    },
    {
      "type": "header",
      "content": "Section Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 32
    },
    {
      "type": "header",
      "content": "Container padding"
    },
    {
      "type": "select",
      "id": "desktop_section_padding_top_bottom",
      "label": "Set the vertical padding (Desktop)",
      "info": "Set the vertical padding for desktop devices",
      "default": "md:py-16",
      "options": [
        {
          "value": "md:py-0",
          "label": "None"
        },
        {
          "value": "md:py-4",
          "label": "PY-4"
        },
        {
          "value": "md:py-6",
          "label": "PY-6"
        },
        {
          "value": "md:py-8",
          "label": "PY-8"
        },
        {
          "value": "md:py-10",
          "label": "PY-10"
        },
        {
          "value": "md:py-12",
          "label": "PY-12"
        },
        {
          "value": "md:py-14",
          "label": "PY-14"
        },
        {
          "value": "md:py-16",
          "label": "PY-16"
        },
        {
          "value": "md:py-20",
          "label": "PY-20"
        },
        {
          "value": "md:py-24",
          "label": "PY-24"
        },
        {
          "value": "md:py-28",
          "label": "PY-28"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_top_bottom",
      "label": "Set the vertical padding (Mobile)",
      "info": "Set the vertical padding for mobile devices",
      "default": "py-4",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-4",
          "label": "PY-4"
        },
        {
          "value": "py-6",
          "label": "PY-6"
        },
        {
          "value": "py-8",
          "label": "PY-8"
        },
        {
          "value": "py-10",
          "label": "PY-10"
        },
        {
          "value": "py-12",
          "label": "PY-12"
        },
        {
          "value": "py-14",
          "label": "PY-14"
        }
      ]
    },
    {
      "type": "select",
      "id": "desktop_container_padding_left_right",
      "label": "Set left right Container padding for desktop",
      "info": "Set the left and right padding for the container on desktop devices",
      "default": "md:px-16",
      "options": [
        {
          "value": "md:px-0",
          "label": "None"
        },
        {
          "value": "md:px-4",
          "label": "PX-4"
        },
        {
          "value": "md:px-6",
          "label": "PX-6"
        },
        {
          "value": "md:px-8",
          "label": "PX-8"
        },
        {
          "value": "md:px-10",
          "label": "PX-10"
        },
        {
          "value": "md:px-12",
          "label": "PX-12"
        },
        {
          "value": "md:px-14",
          "label": "PX-14"
        },
        {
          "value": "md:px-16",
          "label": "PX-16"
        },
        {
          "value": "md:px-20",
          "label": "PX-20"
        },
        {
          "value": "md:px-24",
          "label": "PX-24"
        },
        {
          "value": "md:px-28",
          "label": "PX-28"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_container_padding_left_right",
      "label": "Set padding for the left and right of the container on mobile devices",
      "default": "px-4",
      "options": [
        {
          "value": "px-0",
          "label": "None"
        },
        {
          "value": "px-4",
          "label": "PX-4"
        },
        {
          "value": "px-6",
          "label": "PX-6"
        },
        {
          "value": "px-8",
          "label": "PX-8"
        },
        {
          "value": "px-10",
          "label": "PX-10"
        },
        {
          "value": "px-12",
          "label": "PX-12"
        },
        {
          "value": "px-14",
          "label": "PX-14"
        }
      ]
    },
    {
      "type": "header",
      "content": "Solid button block"
    },
    {
      "type": "text",
      "id": "btn_primary_text",
      "label": "Button text",
      "default": "Primary button",
      "info": "Leave empty to disable the button. Enter text to display on the button."
    },
    {
      "type": "url",
      "id": "btn_primary_url",
      "label": "Btton URL",
      "info": "Enter the URL where the button should redirect when clicked."
    },
    {
      "type": "select",
      "id": "btn_primary_target",
      "label": "Open link",
      "info": "Choose whether the link should open in the same or a new tab.",
      "default": "_self",
      "options": [
        {
          "value": "_blank",
          "label": "Open in a new tab"
        },
        {
          "value": "_self",
          "label": "Open in same tab"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "multicolumn",
      "name": "Multicolumn",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Add text for image",
          "default": "Increased Foot Traffic"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Multicolumn",
      "blocks": [
        {
          "type": "multicolumn"
        },
        {
          "type": "multicolumn"
        },
        {
          "type": "multicolumn"
        },
        {
          "type": "multicolumn"
        }
      ]
    }
  ]
}
{% endschema %}
