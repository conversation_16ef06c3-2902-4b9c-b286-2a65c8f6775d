<div class="container px-4 lg:px-0">
  <div class="main-wrapper mx-auto max-w-[600px] mb-7 mt-9 md:mb-0 md:mt-[60px]">
    <div class="logo-block flex justify-center mb-5">{{- 'styku-logo.svg' | inline_asset_content -}}</div>
    <div class="header-block mb-11 text-center max-w-[560px]">
      <h2 id="title" class="text-[28px] font-bold text-secondary leading-9 mb-2.5">
        {{ 'sections.email_verification.title' | t }}
      </h2>
      <p id="description" class="paragraph-text">{{ 'sections.email_verification.description' | t }}</p>
    </div>
    <form id="emailVerificationCode" class="form-block">
      <div class="input-block mb-4">
        <label class="block mb-2 text-base text-secondary font-bold">{{ 'input.email_verification.label' | t }}</label>
        <input
          type="text"
          id="verificationCode"
          name="verificationCode"
          placeholder="{{ 'input.email_verification.placeholder' | t }}"
          class="bg-white border text-base border-gray-2 text-secondary placeholder-gray-4 rounded-md w-full py-2.5 px-3 focus:border-gray-2 focus:outline-none"
        >
      </div>
      <div class="link-block leading-[0px]">
        <span class="text-sm">{{- 'general.customer.not_get_email' | t }}</span>
        <button
          id="resendVerificationCode"
          type="button"
          role="button"
          class="cursor-pointer text-sm text-primary-gradient"
        >
          {{ 'general.customer.resend_email' | t }}
        </button>
      </div>
      <div class="button-block mt-6">
        <button
          id="submitButton"
          type="submit"
          class="button-primary-gradient flex justify-center items-center gap-4 !text-base w-full !py-3"
          disabled
          role="button"
        >
          {% render 'white-spinner-icon' %}
          <span>{{ 'general.customer.next' | t }}</span>
        </button>
      </div>
    </form>
  </div>
</div>

<script src="{{ 'email-verification.js' | asset_url }}" defer></script>
{% schema %}
{
  "name": "Email verification",
  "class": "index-section"
}
{% endschema %}
