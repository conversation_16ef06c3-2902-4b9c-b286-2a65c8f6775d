document.addEventListener('DOMContentLoaded', function () {
  const summeryHeadingTitle = document.querySelector('.summery-heading-title');
  const sidebarLinksItem = document.querySelectorAll('.sidebar-link-item');
  const lastScan = document.querySelector('.last-scan-date');
  const scansCount = document.querySelector('.number-of-scans-count');
  const isMobile = window.matchMedia('(max-width: 768px)').matches;
  const accountLinkBlock = document.querySelectorAll('.header-profile-link-block .account-links');
  const exceptionMessage = window.errorMessage;

  // Reload browser on popstate
  reloadOnPopState();

  // Function to show a specific membership block
  function toggleMembershipVisibility(targetBlockId) {
    const targetBlock = document.getElementById(targetBlockId);

    if (targetBlock) {
      targetBlock.classList.remove('hidden');
    }

    document.querySelectorAll('.membership-block').forEach((block) => {
      if (block.id !== targetBlockId) {
        block.classList.add('hidden');
      }
    });
  }

  // Get access token from the local storage
  const accessToken = getLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);

  // Fetch profile data and update UI with profile data
  async function updateUIWithProfileData() {
    const profile = await getProfile(accessToken);

    if (profile) {
      // Update scans count and last scan date
      scansCount.textContent = profile.scansCount;
      lastScan.textContent = formatDate(profile.lastScanDate);
    }
  }

  // Function to update active color of links based on hash URL
  async function updateSidebarLink() {
    const hash = window.location.hash;

    sidebarLinksItem.forEach(function (link) {
      if (isMobile) {
        link.closest('details').removeAttribute('open');
      }

      if (link.getAttribute('href') === hash) {
        const linkText = link.textContent.trim();
        summeryHeadingTitle.textContent = linkText;
        sidebarLinksItem.forEach(function (link) {
          link.classList.remove('active-link');
        });

        link.classList.add('active-link');

        // Show and hide the section based on the URL parameters
        toggleMembershipVisibility(hash.substring(1));
      }
    });

    if (hash === HASH_KEY.MEMBERSHIP) {
      await updateMembershipBlock(hash);
    }
  }

  // Function to handle link clicks
  async function handleLinkClick(event) {
    event.preventDefault();

    const targetBlockIdToShow = this.getAttribute('data-block-id');

    // Show and hide the section based on the clicked link item
    toggleMembershipVisibility(targetBlockIdToShow);

    const clickedLinkText = this.textContent.trim();
    summeryHeadingTitle.textContent = clickedLinkText;

    sidebarLinksItem.forEach(function (link) {
      link.classList.remove('active-link');
    });

    this.classList.add('active-link');

    const targetAccountElement = document.querySelector(`a.account-links[data-block-id="${targetBlockIdToShow}"]`);
    accountLinkBlock.forEach((item) => item.classList.remove('active-link'));
    targetAccountElement.classList.add('active-link');

    // Get the Hash form the attribute
    const hash = this.getAttribute('href');

    if (hash === HASH_KEY.MEMBERSHIP) {
      await updateMembershipBlock(hash);
    } else {
      // Update URL hash
      window.history.replaceState({}, '', `${hash}`);
    }

    // Close the dropdown if it's mobile
    if (isMobile) {
      this.closest('details').removeAttribute('open');
    }
    window.scrollTo({
      top: 0,
      behavior: 'auto',
    });
  }

  sidebarLinksItem.forEach(function (link, index) {
    link.addEventListener('click', handleLinkClick);

    if (index === 0) {
      link.classList.add('active-link');
    }
  });

  async function updateMembershipBlock(hash) {
    try {
      toggleFullScreenPreloader(true);
      window.history.replaceState({}, '', `${hash}`);

      const membershipBlockElement = document.querySelector(`${HASH_KEY.MEMBERSHIP}`);

      const response = await fetch(window.location.href);

      if (!response.ok) throw new Error(`Network response was not ok: ${response.statusText}`);

      const responseData = await response.text();
      const parsedDocument = new DOMParser().parseFromString(responseData, 'text/html');
      const newMembershipElement = parsedDocument.querySelector(`${HASH_KEY.MEMBERSHIP}`);

      // Get the Health Pass Element form the new document
      const newHealthPassBlockEl = newMembershipElement.querySelector('#healthPass');

      if (newHealthPassBlockEl) {
        const healthPassSubscriptionTitleEl = newHealthPassBlockEl.querySelector('#healthPassSubscriptionTitle');
        const healthPassValidDateEL = newHealthPassBlockEl.querySelector('.validDate');

        const subscriptionHeaderBlock = newHealthPassBlockEl.querySelector('.subscription-header-block');
        const skeletonTarget = newHealthPassBlockEl.querySelectorAll('.skeleton-target');
        const skeleton = newHealthPassBlockEl.querySelectorAll('.skeleton');

        const subscription = await getSubscriptions();

        if (subscription) {
          const nextBillingDate = formatDate(subscription.nextBillingDate);
          healthPassValidDateEL.textContent = ` ${nextBillingDate}`;

          const contractDetails = JSON.parse(subscription.contractDetailsJSON);
          healthPassSubscriptionTitleEl.querySelector('.subscription-title').textContent = contractDetails[0].title;
          const status = healthPassSubscriptionTitleEl.querySelector('.status');
          status.textContent = subscription.status.toUpperCase();

          switch (subscription.status) {
            case SUBSCRIPTION_STATUS.ACTIVE:
              status.classList.add('bg-green-100', 'text-green-800');
              break;
            case SUBSCRIPTION_STATUS.CANCELLED:
              status.classList.add('bg-red-100', 'text-red-800');
              break;
            default:
              status.classList.add('bg-blue-100', 'text-blue-800');
              break;
          }
        }

        skeleton.forEach((skeletonElement) => {
          skeletonElement.remove();
        });

        skeletonTarget.forEach((skeletonTargetElement) => {
          skeletonTargetElement.classList.remove('hidden');
        });

        subscriptionHeaderBlock.classList.remove('animate-pulse');

        membershipBlockElement.replaceWith(newMembershipElement);
        newMembershipElement.classList.remove('hidden');

        newMembershipElement.querySelector('#moreDetails').addEventListener('click', function (event) {
          event.preventDefault();
          const subscriptions = { subscriptions: 'subscriptions' };
          updateHistoryState(subscriptions, PAGE_URLS.SUBSCRIPTIONS_LIST);
        });
      }
    } catch (error) {
      console.error('Error updating membership:', error);
    } finally {
      toggleFullScreenPreloader(false);
    }
  }

  async function getSubscriptions() {
    try {
      const subscriptionResponse = await fetchData(API_PAYLOADS.GET_SUBSCRIPTIONS);
      const subscriptions = await subscriptionResponse.json();

      // Get the latest subscription
      return getLatestSubscription(subscriptions);
    } catch (error) {
      showToastMessage(exceptionMessage.somethingWentWrong);
      console.error('Error while fetching subscription data:', error);
      throw error;
    }
  }

  // Get the latest updated subscription from the list of subscriptions
  function getLatestSubscription(subscriptions) {
    if (!subscriptions || subscriptions.length === 0) return null;

    // Filter out the Active subscriptions
    const activeSubscriptions = subscriptions.filter(
      (subscription) => subscription.status === SUBSCRIPTION_STATUS.ACTIVE
    );

    if (activeSubscriptions.length > 0) {
      // Sort active subscriptions by updatedAt descending
      activeSubscriptions.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
      return activeSubscriptions[0];
    } else {
      // All subscriptions are cancelled, return the one with the latest updatedAt subscriptions
      subscriptions.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
      return subscriptions[0];
    }
  }

  // Check if the link hash is in the location URL
  updateSidebarLink();

  // Update UI with profile data
  updateUIWithProfileData();
});
