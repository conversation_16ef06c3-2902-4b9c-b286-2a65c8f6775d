{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="faq-section pb-12 md:pb-20 section-{{ section.id }}-margin">
  <div class="container px-6 lg:px-0">
    <div class="text-block w-full text-center mx-auto mb-16">
      <h1 class="section-heading-40 text-secondary font-bold">
        {{ section.settings.title }}
      </h1>
      {% if section.settings.unable_paragraph %}
        <div class="mt-3 text-base">{{ section.settings.paragraph }}</div>
      {% endif %}
    </div>
    <div class="content-block max-w-3xl mx-auto">
      <div class="accordion">
        {%- for block in section.blocks -%}
          <div class="accordion-item border-b border-gray-2 last:border-none mb-6 md:mb-8">
            <button
              aria-expanded="false"
              class="accordion-title-block flex justify-between gap-7 w-full text-left"
            >
              <span class="grow text-base md:text-lg text-secondary font-medium text-left">
                {{- block.settings.question -}}
              </span>
              <span class="flex-none w-10 icon" aria-hidden="true"></span>
            </button>
            <div class="accordion-body accordion-content w-[95%] mb-6 md:mb-8 mt-2">
              <div class="paragraph-text-responsive text-left">{{ block.settings.answer }}</div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Faq section",
  "class": "index-section",
  "max_blocks": 15,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Questions",
      "default": "Frequently Asked Questions"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add answer",
      "default": "<p>Lorem ipsum dolor sit amet consectetur.</p>"
    },
    {
      "type": "checkbox",
      "id": "unable_paragraph",
      "label": "Show and hide the paragraph Block",
      "default": false
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "FAQs",
      "name": "FAQs",
      "settings": [
        {
          "type": "text",
          "id": "question",
          "label": "Add Question",
          "default": "What are some other benefits of Healthpass?"
        },
        {
          "type": "richtext",
          "id": "answer",
          "label": "Paragraph",
          "default": "<p>Healthpass provides a litany of information related to your body such as accurate body composition measurement, circumference measurements, postural analysis, identifying areas of improvement, progress tracking, and motivation to make positive changes. The whole body scan and health screen details provide you with the necessary tools to understand your health, improving health literacy. Healthpass locations are everywhere and accessible, making it easy to check and monitor your health on your time, when it’s convenient for you."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Faq section",
      "blocks": [
        {
          "type": "FAQs"
        },
        {
          "type": "FAQs"
        },
        {
          "type": "FAQs"
        },
        {
          "type": "FAQs"
        },
        {
          "type": "FAQs"
        }
      ]
    }
  ]
}
{% endschema %}
