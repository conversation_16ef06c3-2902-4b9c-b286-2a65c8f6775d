<div
  data-popover
  id="cart-popup"
  role="tooltip"
  class="absolute z-50 invisible inline-block w-[432px] max-w-full text-sm text-gray-5 transition-opacity duration-300 bg-white border border-gray-2 rounded-2xl shadow-lg opacity-0"
>
  <div class="cart-popup-header-block">
    {% unless cart.item_count == 0 %}
      <div class="cart-popup-header px-5 pt-5 mb-4">
        <h3 id="cart-popup-label" class="heading-level-4">
          {{ 'cart.title' | t }}
        </h3>
      </div>
    {% endunless %}
  </div>
  <form class="flex flex-col gap-4" method="post" action="{{ routes.cart_url }}">
    <div class="cart-body flex flex-col gap-4">
      {% if cart.item_count == 0 %}
        <div class="p-6">
          {% render 'empty-cart', title_classes: 'text-xl font-bold text-secondary mt-4 mb-2', border: true %}
        </div>
      {% else %}
        {% render 'customer-order-comparison-for-cart-items',
          cart_items: cart.items,
          title_classes: 'text-base font-bold text-secondary',
          cart_popup: true,
          list_wrapper_classes: 'max-h-[270px] overflow-y-auto custom-scrollbar mr-1.5 pt-0 pr-1.5 pb-5 pl-5'
        %}
      {% endif %}
    </div>
  </form>
</div>
