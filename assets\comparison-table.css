.compare-table-section .float-bg {
  opacity: 0.5;
  width: 100%;
  height: 347px;
  background: linear-gradient(#e2e8e8 0%, var(--color-white) 100%);
  z-index: -5;
}
.comparison-table-wrapper-block {
  border-radius: 16px;
  border: 2px solid var(--color-gray-1);
  background: var(--color-white);
}
.comparison-card-item {
  border-radius: 16px;
  overflow: hidden;
}
.comparison-cards .compare-card {
  padding: 13px 16px;
}
tbody tr:nth-child(odd),
.comparison-cards .compare-card:nth-child(odd) {
  background-color: var(--color-gray-1);
}
tbody tr:nth-child(even),
.comparison-cards .compare-card:nth-child(even) {
  background-color: var(--color-white);
}
.comparison-card-item:first-child {
  border-radius: 16px;
  border: 2px solid var(--color-primary);
  background: var(--color-white);
}
.comparison-card-item:first-child .card-header {
  border-radius: 8px 8px 0 0;
  border: 2px solid var(--color-primary);
  background: var(--gradient-primary);
}
.comparison-card-item:first-child .feature-value {
  font-weight: 700;
}
.comparison-card-item:first-child .card-header h2 {
  color: var(--color-white) !important;
}
.comparison-cards::-webkit-scrollbar {
  width: 0 !important;
  background: transparent !important;
}
.comparison-table tbody tr td {
  padding: 8px 16px;
  color: var(--color-gray-8);
  text-align: center;
  font-size: 16px;
  max-width: 11rem;
}
.comparison-table tbody tr td:first-child {
  padding: 8px 16px;
  color: var(--color-gray-8);
  text-align: left;
  font-size: 16px;
  max-width: 11rem;
}
.comparison-table thead th {
  padding: 8px 16px;
  color: var(--color-secondary);
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  line-height: normal;
  max-width: 11rem;
}
.comparison-table thead th:nth-child(2) {
  border-radius: 8px 8px 0 0;
  border: 2px solid var(--gradient-primary);
  background: var(--gradient-primary);
  color: var(--color-white) !important;
}
.comparison-table tbody tr td:nth-child(2) {
  border-left: 2px solid var(--color-primary);
  border-right: 2px solid var(--color-primary);
  font-weight: 700;
}
.comparison-cards-inner-wrapper::-webkit-scrollbar {
  width: 0 !important;
  background: transparent !important;
}
