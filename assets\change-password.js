document.addEventListener('DOMContentLoaded', function () {
  const changePasswordForm = document.getElementById('changePasswordForm');
  const oldPassword = document.getElementById('oldPassword');
  const newPassword = document.getElementById('newPassword');
  const confirmPassword = document.getElementById('confirmPassword');
  const errorMessageGroup = document.querySelector('.error-message-group');
  const minimumCharactersPasswordMessage = errorMessageGroup.querySelector('.minimum-characters-message');
  const upperLowerCaseMessage = errorMessageGroup.querySelector('.upper-lowercase-message');
  const numberPasswordMessage = errorMessageGroup.querySelector('.number-message');
  const validConfirmPasswordMessage = document.querySelector('.valid-password');
  const invalidConfirmPasswordMessage = document.querySelector('.invalid-password');
  const specialCharactersMessage = errorMessageGroup.querySelector('.special-characters-message');
  const submitButton = document.getElementById('submitChangePassword');
  const changePasswordModalCloseElement = document.querySelector('[data-modal-hide="changePasswordModal"]');
  const spinnerIcon = document.getElementById('spinnerIcon');
  const successMessage = window.successMessage;
  const exceptionMessage = window.errorMessage;

  // Event listeners for current input password
  oldPassword.addEventListener('input', enableSubmit);

  // Event listener for new password input
  newPassword.addEventListener('input', function () {
    enableSubmit();

    const passwordValue = newPassword.value;
    // Check if password length is at least 8 characters
    if (isValidLength(passwordValue, 8)) {
      updatePasswordValidationMessage(minimumCharactersPasswordMessage, true);
      updatePasswordValidationIcon(minimumCharactersPasswordMessage, true);
    } else {
      updatePasswordValidationMessage(minimumCharactersPasswordMessage, false);
      updatePasswordValidationIcon(minimumCharactersPasswordMessage, false);
    }

    if (checkUpperAndLowerCase(passwordValue)) {
      updatePasswordValidationMessage(upperLowerCaseMessage, true);
      updatePasswordValidationIcon(upperLowerCaseMessage, true);
    } else {
      updatePasswordValidationMessage(upperLowerCaseMessage, false);
      updatePasswordValidationIcon(upperLowerCaseMessage, false);
    }

    // Check if password contains at least one digit
    if (containsDigit(passwordValue)) {
      updatePasswordValidationMessage(numberPasswordMessage, true);
      updatePasswordValidationIcon(numberPasswordMessage, true);
    } else {
      updatePasswordValidationMessage(numberPasswordMessage, false);
      updatePasswordValidationIcon(numberPasswordMessage, false);
    }
    // Checks the special characters from the set [` ~ ! @ # $ % ^ & * ( ) _ + - = [ ] { } \ | ; : " ' , . < > / ?]
    if (containsSpecialChar(passwordValue)) {
      updatePasswordValidationMessage(specialCharactersMessage, true);
      updatePasswordValidationIcon(specialCharactersMessage, true);
    } else {
      updatePasswordValidationMessage(specialCharactersMessage, false);
      updatePasswordValidationIcon(specialCharactersMessage, false);
    }

    // Check if confirm password input has value, then validate by new password
    if (confirmPassword.value) {
      validatePasswords();
    }
    hideConfirmPasswordValidationMessage();
  });

  // Event listener for confirm password input
  confirmPassword.addEventListener('input', function () {
    enableSubmit();
    validatePasswords();
    hideConfirmPasswordValidationMessage();
  });

  // Function to hide confirm password validation messages if both passwords are empty
  function hideConfirmPasswordValidationMessage() {
    if (!newPassword.value && !confirmPassword.value) {
      validConfirmPasswordMessage.classList.add('hidden');
      invalidConfirmPasswordMessage.classList.add('hidden');
    }
  }

  // Function to validate passwords
  function validatePasswords() {
    if (isPasswordsMatch(newPassword, confirmPassword)) {
      // Passwords match
      updatePasswordValidationIcon(validConfirmPasswordMessage, true);
      updatePasswordValidationMessage(validConfirmPasswordMessage, true);
      validConfirmPasswordMessage.classList.remove('hidden');
      invalidConfirmPasswordMessage.classList.add('hidden');
    } else {
      // Passwords do not match
      updatePasswordValidationIcon(invalidConfirmPasswordMessage, false);
      updatePasswordValidationMessage(invalidConfirmPasswordMessage, false);
      validConfirmPasswordMessage.classList.add('hidden');
      invalidConfirmPasswordMessage.classList.remove('hidden');
    }
  }

  // Function to enable/disable submit button based on form validity
  function enableSubmit() {
    submitButton.disabled = !(isInputValid(oldPassword) && isPasswordsMatch(newPassword, confirmPassword));
  }

  // Function to check if input field is not empty
  function isInputValid(input) {
    return !!input.value;
  }

  // Function to check if password is valid
  function isPasswordValid(input) {
    // Should be 8 character long and contain one digit
    return isInputValid(input) && isValidLength(input.value, 8) && containsDigit(input.value);
  }

  // Function to check if passwords match
  function isPasswordsMatch(newPasswordEle, confirmPasswordEle) {
    return (
      isPasswordValid(newPasswordEle) &&
      isPasswordValid(confirmPasswordEle) &&
      newPasswordEle.value === confirmPasswordEle.value
    );
  }

  // Attach reset function to modal close button
  changePasswordForm.addEventListener('submit', async function (event) {
    event.preventDefault();

    submitButton.disabled = true;
    spinnerIcon.classList.remove('hidden');

    // Extract form data and construct a request body object
    const formData = new FormData(changePasswordForm);
    const requestBody = {};
    formData.forEach((value, key) => {
      requestBody[key] = value;
    });

    try {
      const accessToken = getLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      const profile = getLocalStorageItem(LOCAL_STORAGE_KEY.LOGGED_IN_USER);

      const changePasswordPayload = API_PAYLOADS.CHANGE_PASSWORD(accessToken);
      changePasswordPayload.body = JSON.stringify(requestBody);

      const response = await fetchData(changePasswordPayload);

      if (response.ok) {
        const responseData = await response.json();

        switch (responseData.status) {
          case SUCCESS_CODE.SUCCESS:
            changePasswordModalCloseElement.click();
            showToastMessage(successMessage.passwordChangedSuccessfully);

            setTimeout(async () => {
              await fetchData(API_PAYLOADS.SHOPIFY_CUSTOMER_SIGN_OUT);
              deleteLocalStorageItem(LOCAL_STORAGE_KEY.LOGGED_IN_USER);
              deleteLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
              window.location.replace(PAGE_URLS.SHOPIFY_LOGIN);
            }, 5000);

            // Login into the shopify
            await shopifyCustomerLogin(profile.email, requestBody.newPassword);
            break;
          case ERROR_CODES.WEAK_PASSWORD:
            showToastMessage(exceptionMessage.weakPasswordMissingLetter);
            break;
          case ERROR_CODES.INVALID_PASSWORD:
            showToastMessage(exceptionMessage.oldPasswordNotMatchingCurrent);
            break;
          case ERROR_CODES.NEW_PASSWORD_SAME_AS_OLD:
            showToastMessage(exceptionMessage.newPasswordNotSameAsOld);
            break;
          default:
            showToastMessage(exceptionMessage.somethingWentWrong);
            break;
        }
      } else {
        showToastMessage(exceptionMessage.somethingWentWrong);
      }
    } catch (error) {
      showToastMessage(exceptionMessage.somethingWentWrong);
      console.error('Error while changing password:', error);
    } finally {
      submitButton.disabled = false;
      spinnerIcon.classList.add('hidden');
    }
  });
});
