{{ 'media-with-text-second-option.css' | asset_url | stylesheet_tag }}
{%- style -%}
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
      margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-margin {
        margin-top: {{ section.settings.margin_top }}px;
        margin-bottom: {{ section.settings.margin_bottom }}px;
      }
      .overlap-mechine-image img {
        max-width: 690px;
      }
    }
    .bg-bolor {
      background: {% if section.settings.bg_color == blank %}
        radial-gradient(44.51% 18.78% at 50.03% 0%, #F3F3F3 0%, var(--color-white) 100%)
      {% else %}
      {{ section.settings.bg_color }}
    {% endif %}
  }

  .description li strong {
    color: var(--color-secondary);
  }
{%- endstyle -%}

<div class="relative media-with-text-wrapper bg-bolor section-{{ section.id }}-margin {{ section.settings.desktop_section_padding_top }} {{ section.settings.desktop_section_padding_bottom }} {{ section.settings.mobile_section_padding_top }} {{ section.settings.mobile_section_padding_bottom }}">
  {% unless section.settings.section_heading == blank %}
    <div class="flex flex-col container px-4 md:px-0 justify-start md:justify-center w-full {{ section.settings.block_max_width }} lg:text-left">
      <h1 class="heading-level-2 text-secondary text-center">
        {{ section.settings.section_heading }}
      </h1>
      {% unless section.settings.section_paragraph == blank %}
        <div class="paragraph-text text-center mt-2 md:mb-12">
          {{ section.settings.section_paragraph }}
        </div>
      {% endunless %}
    </div>
  {% endunless %}
  {% for block in section.blocks %}
    {% if block.settings.align_content_block > 0 %}
      {% if forloop.index %}
        {% style %}
          .list-block-checkmark-content:has(.ovrelap-image-block),
          .list-block-content:has(.ovrelap-image-block) {
            margin-top:{{- block.settings.align_content_block -}}px;
          }
        {% endstyle %}
      {% endif %}
    {% endif %}
    <div class="container px-4 lg:px-0 py-8 media-with-text-inner-wrapper">
      <div class="flex flex-col md:grid-cols-2 justify-center md:justify-between gap-6 {{ block.settings.desktop_block_gap }} {{ block.settings.media_align_desktop }} {{ block.settings.paragraph_class | append: '-main-wrapper' }}">
        <div class="image-block-media-with-text flex items-center justify-center w-full md:w-2/4">
          {% if block.settings.img != blank %}
            {%- liquid
              assign sizes = '100vw'
              assign widths = '750, 1100, 1500, 1780'
              assign fetch_priority = 'high'
            -%}
            {{
              block.settings.img
              | image_url: width: 3840
              | image_tag:
                loading: 'lazy',
                sizes: sizes,
                widths: widths,
                fetchpriority: fetch_priority,
                class: 'w-full lazy-load blur-loading'
            }}
          {%- else -%}
            {{- 'image' | placeholder_svg_tag: 'w-[430px] h-[270px] border border-gray-7 rounded-xl' -}}
          {%- endif -%}
        </div>
        <div class="flex flex-col justify-start {{ block.settings.content_block_alignment }} lg:text-left w-full md:w-2/4 {{ block.settings.paragraph_class | append: '-content' -}}">
          {% unless block.settings.title == blank %}
            {% unless block.settings.subtitle == blank %}
              <p class="text-base text-gray-3">{{- block.settings.subtitle -}}</p>
            {% endunless %}
            <h2 class="title {{ block.settings.title_font_size }} mb-2">
              {{ block.settings.title }}
            </h2>
          {% endunless %}
          {% unless block.settings.description == blank %}
            <div class="[&_div]:space-y-5">
              <div class="description paragraph-text {{ block.settings.paragraph_class }}">
                {{ block.settings.description }}
              </div>
            </div>
            {% if block.settings.HSA_FSA_badge %}
              <div class="mt-8">
                <span class="p-2 bg-[#53cc2824] gap-2 rounded-md inline-flex">
                  <span class="text-sm text-[#53CC28] font-bold">{{ 'sections.health_product.badge' | t }}</span>
                </span>
              </div>
            {% endif %}
          {% endunless %}
          {% unless block.settings.btn_text == blank %}
            <div class="button-block mt-12 inline-flex">
              <a
                href="{{- block.settings.btn_url -}}"
                target="{{- block.settings.btn_target -}}"
                class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if block.settings.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
              >
                {% if block.settings.btn_variant != 'solid_primary' %}
                  <span class="text-primary-gradient text-sm font-bold">
                    {{- block.settings.btn_text -}}
                  </span>
                {% else %}
                  {{- block.settings.btn_text -}}
                {% endif %}
              </a>
            </div>
          {% endunless %}
          {% if block.settings.second_image != blank %}
            <div class="overlap-mechine-image absolute right-0 bottom-40 ovrelap-image-block">
              <img
                src="{{ block.settings.second_image | image_url: width: 1200 }}"
                alt="{{ section.settings.second_image.alt }}"
                width="{{ section.settings.second_image.width }}"
                height="{{ section.settings.second_image.height }}"
                class="object-contain select-none lazy-load blur-loading"
                style="-webkit-user-drag: none;"
              >
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  {% endfor %}
  {% unless section.settings.section_btn_text == blank %}
    <div class="flex flex-col md:flex-row justify-center">
      <div class="button-block mt-12 inline-flex">
        <a
          href="{{- section.settings.section_btn_url -}}"
          target="{{- section.settings.section_btn_target -}}"
          class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if section.settings.section_btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
        >
          {% if section.settings.section_btn_variant != 'solid_primary' %}
            <span class="text-primary-gradient text-sm font-bold">
              {{- section.settings.section_btn_text -}}
            </span>
          {% else %}
            {{- section.settings.section_btn_text -}}
          {% endif %}
        </a>
      </div>
    </div>
  {% endunless %}
</div>

{% schema %}
{
  "name": "Media with text",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "select",
      "id": "block_max_width",
      "label": "Set max width for section",
      "default": "md:max-w-full",
      "options": [
        {
          "value": "md:max-w-none",
          "label": "Max-W-None"
        },
        {
          "value": "md:max-w-xs",
          "label": "Max-W-XS"
        },
        {
          "value": "md:max-w-sm",
          "label": "Max-W-SM"
        },
        {
          "value": "md:max-w-md",
          "label": "Max-W-MD"
        },
        {
          "value": "md:max-w-lg",
          "label": "Max-W-LG"
        },
        {
          "value": "md:max-w-xl",
          "label": "Max-W-XL"
        },
        {
          "value": "md:max-w-2xl",
          "label": "Max-W-2XL"
        },
        {
          "value": "md:max-w-3xl",
          "label": "Max-W-3XL"
        },
        {
          "value": "md:max-w-4xl",
          "label": "Max-W-4XL"
        },
        {
          "value": "md:max-w-5xl",
          "label": "Max-W-5XL"
        },
        {
          "value": "md:max-w-6xl",
          "label": "Max-W-6XL"
        },
        {
          "value": "md:max-w-7xl",
          "label": "Max-W-7XL"
        },
        {
          "value": "md:max-w-full",
          "label": "Max-W-Full"
        }
      ]
    },
    {
      "type": "html",
      "id": "section_heading",
      "label": "Section heading"
    },
    {
      "type": "richtext",
      "id": "section_paragraph",
      "label": "Section Paragraph"
    },
    {
      "type": "color_background",
      "id": "bg_color",
      "label": "Background color"
    },
    {
      "type": "header",
      "content": "Section Spacing"
    },
    {
      "type": "select",
      "id": "desktop_section_padding_top",
      "label": "Set Padding for the desktop",
      "default": "md:pt-16",
      "options": [
        {
          "value": "md:pt-0",
          "label": "PT-0"
        },
        {
          "value": "md:pt-4",
          "label": "PT-4"
        },
        {
          "value": "md:pt-6",
          "label": "PT-6"
        },
        {
          "value": "md:pt-8",
          "label": "PT-8"
        },
        {
          "value": "md:pt-10",
          "label": "PT-10"
        },
        {
          "value": "md:pt-12",
          "label": "PT-12"
        },
        {
          "value": "md:pt-14",
          "label": "PT-14"
        },
        {
          "value": "md:pt-16",
          "label": "PT-16"
        }
      ]
    },
    {
      "type": "select",
      "id": "desktop_section_padding_bottom",
      "label": "Desktop section padding bottom",
      "default": "md:pb-16",
      "options": [
        {
          "value": "md:pb-0",
          "label": "PB-0"
        },
        {
          "value": "md:pb-4",
          "label": "PB-4"
        },
        {
          "value": "md:pb-6",
          "label": "PB-6"
        },
        {
          "value": "md:pb-8",
          "label": "PB-8"
        },
        {
          "value": "md:pb-10",
          "label": "PB-10"
        },
        {
          "value": "md:pb-12",
          "label": "PB-12"
        },
        {
          "value": "md:pb-14",
          "label": "PB-14"
        },
        {
          "value": "md:pb-16",
          "label": "PB-16"
        },
        {
          "value": "md:pb-20",
          "label": "PB-20"
        },
        {
          "value": "md:pb-24",
          "label": "PB-24"
        },
        {
          "value": "md:pb-28",
          "label": "PB-28"
        },
        {
          "value": "md:pb-32",
          "label": "PB-32"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_top",
      "label": "Set section padding for the Mobile",
      "default": "pt-4",
      "options": [
        {
          "value": "pt-0",
          "label": "PT-0"
        },
        {
          "value": "pt-4",
          "label": "PT-4"
        },
        {
          "value": "pt-6",
          "label": "PT-6"
        },
        {
          "value": "pt-8",
          "label": "PT-8"
        },
        {
          "value": "pt-10",
          "label": "PT-10"
        },
        {
          "value": "pt-12",
          "label": "PT-12"
        },
        {
          "value": "pt-14",
          "label": "PT-14"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_bottom",
      "label": "Set section padding for the Mobile",
      "default": "pb-4",
      "options": [
        {
          "value": "pb-0",
          "label": "PB-0"
        },
        {
          "value": "pb-4",
          "label": "PB-4"
        },
        {
          "value": "pb-6",
          "label": "PB-6"
        },
        {
          "value": "pb-8",
          "label": "PB-8"
        },
        {
          "value": "pb-10",
          "label": "PB-10"
        },
        {
          "value": "pb-12",
          "label": "PB-12"
        },
        {
          "value": "pb-14",
          "label": "PB-14"
        }
      ]
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 32
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "section_btn_text",
      "label": "Button text",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "section_btn_url",
      "label": "Btton URL"
    },
    {
      "type": "select",
      "id": "section_btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        {
          "value": "_blank",
          "label": "Open in a New Tab"
        },
        {
          "value": "_self",
          "label": "Open in Same Tab"
        }
      ]
    },
    {
      "type": "select",
      "id": "section_btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "row",
      "name": "Row",
      "settings": [
        {
          "type": "header",
          "content": "General"
        },
        {
          "type": "select",
          "id": "media_align_desktop",
          "label": "Media alignment on desktop",
          "default": "md:flex-row",
          "options": [
            {
              "value": "md:flex-row",
              "label": "Left"
            },
            {
              "value": "md:flex-row-reverse",
              "label": "Right"
            }
          ]
        },
        {
          "type": "select",
          "id": "desktop_block_gap",
          "label": "Set Gap for the desktop",
          "default": "md:gap-4",
          "options": [
            {
              "value": "md:gap-4",
              "label": "Gap-4"
            },
            {
              "value": "md:gap-6",
              "label": "gap-6"
            },
            {
              "value": "md:gap-8",
              "label": "gap-8"
            },
            {
              "value": "md:gap-10",
              "label": "gap-10"
            },
            {
              "value": "md:gap-12",
              "label": "gap-12"
            },
            {
              "value": "md:gap-14",
              "label": "gap-14"
            },
            {
              "value": "md:gap-16",
              "label": "gap-16"
            },
            {
              "value": "md:gap-20",
              "label": "gap-20"
            },
            {
              "value": "md:gap-24",
              "label": "gap-24"
            },
            {
              "value": "md:gap-28",
              "label": "gap-28"
            },
            {
              "value": "md:gap-32",
              "label": "gap-32"
            }
          ]
        },
        {
          "type": "header",
          "content": "Image"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image"
        },
        {
          "type": "header",
          "content": "Text"
        },
        {
          "type": "select",
          "id": "content_block_alignment",
          "label": "Align the content block",

          "default": "md:justify-center",
          "options": [
            {
              "value": "md:justify-normal",
              "label": "Normal"
            },
            {
              "value": "md:justify-start",
              "label": "Start"
            },
            {
              "value": "md:justify-center",
              "label": "Center"
            },
            {
              "value": "md:justify-between",
              "label": "Between"
            },
            {
              "value": "md:justify-stretch",
              "label": "Stretch"
            },
            {
              "value": "md:justify-end",
              "label": "End"
            }
          ]
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle"
        },
        {
          "type": "html",
          "id": "title",
          "label": "Title",
          "default": "Media with text"
        },
        {
          "type": "select",
          "id": "title_font_size",
          "label": "Title font-size",
          "default": "heading-level-2",
          "options": [
            {
              "value": "heading-level-1",
              "label": "Level 1"
            },
            {
              "value": "heading-level-2",
              "label": "Level 2"
            },
            {
              "value": "heading-level-3",
              "label": "Level 3"
            },
            {
              "value": "heading-level-4",
              "label": "Level 4"
            }
          ]
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Description (optional)",
          "default": "<p>Styku conducts a comprehensive 3D body scan, capturing millions of precise data points in just 35 seconds. Research shows that Styku’s body fat percentage estimates align within 2% of DEXA scan results, a gold standard in body composition, underscoring its exceptional accuracy.</p>"
        },
        {
          "type": "select",
          "id": "paragraph_class",
          "label": "Class name for richtext block",
          "default": "simple-block",
          "options": [
            {
              "value": "initial-block",
              "label": "Initial"
            },
            {
              "value": "simple-block",
              "label": "Simple"
            },
            {
              "value": "list-block",
              "label": "List"
            },
            {
              "value": "list-block-checkmark",
              "label": "Primary Check mark"
            },
            {
              "value": "highlighted-block",
              "label": "Highlighted"
            },
            {
              "value": "custom-block",
              "label": "Custom"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "HSA_FSA_badge",
          "label": "HSA & FSA badge",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "second_image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "align_content_block",
          "label": "Align content block manually",
          "min": 0,
          "max": 180,
          "unit": "px",
          "step": 2,
          "default": 0
        },
        {
          "type": "header",
          "content": "Button block"
        },
        {
          "type": "text",
          "id": "btn_text",
          "label": "Button text",
          "info": "Leave empty to disable"
        },
        {
          "type": "url",
          "id": "btn_url",
          "label": "Btton URL"
        },
        {
          "type": "select",
          "id": "btn_target",
          "label": "Open file",
          "default": "_self",
          "options": [
            {
              "value": "_blank",
              "label": "Open in a New Tab"
            },
            {
              "value": "_self",
              "label": "Open in Same Tab"
            }
          ]
        },
        {
          "type": "select",
          "id": "btn_variant",
          "label": "Select button variant",
          "default": "solid_primary",
          "options": [
            {
              "value": "solid_primary",
              "label": "Primary solid button"
            },
            {
              "value": "outline_primary",
              "label": "Primary outline button"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Media with text second option",
      "blocks": [
        {
          "type": "row",
          "settings": {
            "title": "Media With Text",
            "description": "<p>Either show an image, video (MP4) or an external video (YouTube). If no text block is shown, the media will take full section's space.</p>",
            "btn_text": "Primary button"
          }
        }
      ]
    }
  ]
}
{% endschema %}
