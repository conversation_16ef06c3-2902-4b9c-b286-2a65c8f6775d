<svg width="58" height="58" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path d="M29 41.002C12.4 34.459 3.24 29.893 6.9 26.957c-.004 0-.004.002-.011.004-2.976 1.843-6.777 5.384-5.405 10.987A29.348 29.348 0 0 1 0 28.718c0 7.872 3.082 15.01 8.086 20.264 4.073-1.536 7.953-2.994 11.59-4.38a834.873 834.873 0 0 0 9.324-3.6Z" fill="url(#a)" />
	<path d="M49.918 48.982a29.295 29.295 0 0 0 8.08-20.26c0 3.223-.52 6.325-1.477 9.226 1.365-5.603-2.434-9.144-5.41-10.987l-.011-.003c3.66 2.935-5.499 7.498-22.1 14.044 2.892 1.14 6.01 2.339 9.323 3.6 3.643 1.386 7.518 2.847 11.595 4.38Z" fill="url(#b)" />
	<path d="M38.323 44.601a839.924 839.924 0 0 1-9.323-3.6c-2.89 1.14-6.01 2.338-9.324 3.6-3.637 1.386-7.517 2.844-11.59 4.38.04.047.082.09.128.138.307.318.625.633.945.94.065.06.125.117.19.175a33.726 33.726 0 0 0 1.408 1.23 31.641 31.641 0 0 0 1.525 1.164 28.142 28.142 0 0 0 1.296.875c.137.085.277.175.416.258a29.061 29.061 0 0 0 2.613 1.423c.058.025.112.05.167.076a28.84 28.84 0 0 0 2.567 1.054c.151.053.303.116.462.169l-.008.004c.09.03.185.057.279.087.15.052.301.1.453.14.226.072.46.14.693.208l.476.132c.238.06.475.115.714.17.152.039.305.078.455.109.388.084.778.162 1.17.23.097.018.195.038.291.055.252.041.506.073.752.108.207.032.413.058.617.08.214.025.424.05.64.068.265.027.537.043.808.061.169.014.34.026.505.035.447.018.894.03 1.35.03h.004c.454 0 .902-.012 1.351-.032.169-.007.333-.019.503-.033.273-.015.545-.034.811-.061.213-.019.426-.043.638-.068.204-.022.41-.05.615-.08.251-.035.5-.067.75-.108.101-.017.198-.037.299-.056.39-.067.78-.145 1.164-.228.153-.032.307-.071.46-.11.238-.053.476-.11.71-.17.16-.047.32-.09.48-.132.231-.068.463-.136.69-.205.155-.043.303-.091.455-.143.092-.03.186-.057.278-.09l-.01-.001c.159-.053.312-.116.469-.169a29.916 29.916 0 0 0 2.73-1.13c.311-.147.615-.302.919-.46.066-.037.132-.077.204-.109.505-.27 1-.555 1.49-.857a24.44 24.44 0 0 0 1.103-.71c.205-.137.408-.275.604-.42.275-.198.547-.397.814-.6a38.69 38.69 0 0 0 1.16-.937c.324-.278.653-.563.966-.857.06-.06.122-.116.187-.175a31.75 31.75 0 0 0 1.076-1.078 3259.403 3259.403 0 0 1-11.595-4.38Z" fill="url(#c)" />
	<path d="M6.889 26.961c.007-.002.007-.004.011-.004.31-.246.704-.483 1.196-.707.139-.064.274-.122.41-.182.699-.344 1.225-.55 1.434-.633a.119.119 0 0 0 .039-.014c4.935-2.11 8.666-3.199 11.03-4.357l.004-.001c.167-.086.331-.167.486-.25.038-.02.074-.041.112-.064a8.21 8.21 0 0 0 .334-.19c.05-.029.1-.065.149-.093l.255-.17c.057-.037.105-.076.159-.115.074-.054.136-.106.205-.16.054-.043.1-.087.15-.131.06-.053.114-.1.169-.154.048-.048.094-.097.138-.148.046-.048.095-.103.137-.153.042-.053.084-.107.119-.16.04-.053.076-.108.11-.163.036-.055.066-.11.1-.171.03-.053.059-.11.082-.165.03-.06.057-.122.079-.184.024-.058.044-.117.062-.18a2.297 2.297 0 0 0 .096-.387c.008-.067.02-.138.029-.21.005-.064.014-.133.013-.204.002-.072.007-.148.007-.22 0-.075-.007-.156-.007-.232-.008-.074-.01-.152-.018-.228-.008-.085-.023-.168-.036-.253-.01-.08-.023-.159-.034-.24l-.066-.283a3.152 3.152 0 0 0-.059-.247 3.952 3.952 0 0 0-.097-.326c-.023-.079-.043-.157-.072-.242-.041-.128-.095-.264-.141-.396-.028-.071-.048-.14-.077-.214-.08-.21-.168-.429-.266-.654C18.79 4.036 23.46.257 23.322 0c-.001.005-.007.002-.007.002-.299.06-.597.129-.89.198-.14.035-.286.065-.424.1-.277.071-.548.144-.82.222-.156.045-.316.087-.47.134-.265.078-.53.166-.789.251-.158.055-.317.106-.475.164l-.77.279a37.422 37.422 0 0 0-1.217.504c-.14.061-.283.126-.425.193-.26.117-.518.235-.77.362-.103.053-.206.103-.306.157-.89.455-1.755.955-2.591 1.496l-.405.26a37.38 37.38 0 0 0-.631.444c-.152.104-.299.21-.447.32-.197.141-.394.298-.589.45-.151.117-.3.232-.448.357-.186.152-.377.306-.559.46-.152.131-.3.262-.447.391-.173.157-.355.314-.527.48-.15.137-.294.283-.44.426-.168.158-.333.319-.496.487-.152.155-.302.314-.452.475-.145.16-.3.317-.446.479-.275.308-.546.626-.812.946-.12.152-.235.308-.358.459-.154.195-.31.388-.457.587-.123.16-.238.328-.357.495-.137.195-.28.389-.417.585-.112.17-.225.343-.335.518a25.875 25.875 0 0 0-.7 1.129c-.125.21-.25.423-.365.634l-.285.526c-.118.227-.234.455-.349.69-.082.169-.167.332-.248.507-.123.262-.242.524-.356.793-.06.144-.128.285-.185.423a29.24 29.24 0 0 0-.487 1.241c-.033.092-.06.184-.09.276-.12.334-.235.67-.34 1.011-.05.157-.096.315-.141.479-.083.276-.161.553-.233.832-.048.176-.087.356-.133.533-.064.27-.125.541-.185.813-.039.182-.076.369-.11.555-.055.272-.1.54-.144.817-.032.184-.059.37-.09.56a32.48 32.48 0 0 0-.104.85 15.31 15.31 0 0 0-.062.541c-.029.31-.048.628-.07.944A29.132 29.132 0 0 0 0 28.718c.001 3.224.525 6.328 1.485 9.23-1.372-5.603 2.43-9.145 5.405-10.987Z" fill="url(#d)" />
	<path d="M56.522 37.948a29.417 29.417 0 0 0 1.443-10.652c-.007-.153-.02-.31-.029-.461-.02-.316-.043-.634-.07-.944-.019-.182-.042-.362-.063-.543a34.61 34.61 0 0 0-.105-.848c-.028-.19-.058-.376-.087-.56a38.177 38.177 0 0 0-.255-1.372 29.249 29.249 0 0 0-.186-.808 22.51 22.51 0 0 0-.365-1.37l-.14-.479c-.107-.34-.223-.677-.34-1.01-.03-.093-.06-.185-.094-.277-.15-.415-.311-.83-.482-1.241-.06-.141-.128-.28-.184-.422-.12-.27-.236-.531-.362-.794-.078-.175-.166-.338-.248-.507-.11-.235-.225-.46-.349-.69-.09-.175-.186-.349-.284-.523-.118-.217-.24-.427-.362-.638-.102-.175-.21-.352-.318-.525a12.311 12.311 0 0 0-.387-.604 19.01 19.01 0 0 0-.335-.518l-.41-.584c-.124-.167-.24-.335-.359-.495-.15-.199-.305-.392-.455-.587-.124-.15-.239-.307-.364-.457-.262-.322-.533-.64-.811-.949-.148-.16-.3-.32-.447-.478-.15-.161-.3-.32-.451-.475a15.553 15.553 0 0 0-.495-.487c-.147-.143-.293-.29-.442-.427-.17-.165-.35-.322-.526-.479l-.446-.391a32.98 32.98 0 0 0-.56-.46 16.437 16.437 0 0 0-.447-.36c-.193-.15-.39-.304-.59-.447-.146-.11-.294-.216-.445-.32-.21-.152-.418-.297-.63-.444l-.407-.26c-.833-.541-1.7-1.043-2.592-1.496-.102-.055-.204-.104-.308-.157-.25-.127-.509-.245-.764-.362-.145-.065-.285-.132-.427-.193-.253-.11-.507-.216-.76-.318-.153-.063-.31-.125-.461-.186-.255-.095-.51-.187-.768-.28-.157-.057-.315-.108-.475-.163-.26-.085-.52-.173-.786-.251-.157-.047-.316-.09-.471-.134a35.23 35.23 0 0 0-.82-.22c-.141-.037-.285-.067-.426-.102-.293-.07-.59-.14-.886-.198-.006 0-.01-.002-.01-.002-.134.257 4.529 4.037.19 14.076-.096.228-.186.447-.266.656-.027.071-.05.14-.076.212-.049.132-.098.268-.142.396-.027.085-.052.163-.074.242-.034.113-.07.22-.097.326-.02.084-.039.162-.056.247-.023.094-.046.191-.063.283l-.04.24c-.013.085-.026.168-.031.253l-.02.228c-.001.079-.005.157-.008.232 0 .075.007.148.008.22.006.072.006.138.013.204.012.072.02.143.035.21.011.064.018.126.034.193.019.065.04.132.06.193.012.06.036.123.056.18l.078.183c.026.056.053.114.085.17.034.057.064.114.1.17.034.055.07.106.11.16.04.054.077.107.122.16.04.053.088.104.135.154.043.051.09.097.136.148.055.055.11.104.169.154.051.044.098.088.15.132l.206.159c.054.039.102.078.158.112.08.058.168.116.254.17.052.033.098.065.152.097.105.063.217.127.333.19a9.959 9.959 0 0 0 .596.313l.01.001c2.363 1.158 6.092 2.25 11.026 4.36l.036.013c.213.081.74.287 1.434.628.139.065.272.12.409.185.497.224.891.46 1.198.707l.011.004c2.977 1.842 6.776 5.384 5.41 10.987Z" fill="url(#e)" />
	<path d="M34.97 19.735a4.444 4.444 0 0 1-.139-.148c-.046-.048-.094-.103-.137-.153a1.73 1.73 0 0 1-.117-.16 3.346 3.346 0 0 1-.113-.16c-.035-.058-.066-.114-.097-.17-.033-.056-.061-.114-.089-.169a3.148 3.148 0 0 1-.135-.364 1.393 1.393 0 0 1-.055-.193c-.016-.067-.03-.13-.04-.194-.011-.066-.024-.137-.029-.21a2.604 2.604 0 0 1-.014-.209c-.004-.047-.005-.098-.007-.148.003-.052.007-.105.008-.156.002-.07.006-.138.013-.21.012-.08.02-.158.037-.236.009-.072.016-.145.032-.219l.06-.263c.014-.074.03-.149.051-.227.025-.095.059-.2.087-.3.025-.074.04-.147.068-.223.04-.117.088-.242.135-.37.023-.063.04-.125.068-.19.074-.195.157-.398.244-.603.61-1.411 1.027-2.683 1.297-3.836 1.205-4.447.433-7.28-.367-8.895.037.007.074.016.113.02.256.067.504.139.758.212.145.037.29.077.433.12.244.074.484.154.725.234.146.046.292.096.438.15.241.082.475.169.709.256.142.053.285.115.424.168.235.099.468.194.7.298.135.055.26.112.393.177.236.106.477.218.707.336.099.044.192.094.286.144.821.416 1.618.879 2.39 1.378.122.078.248.161.37.241.196.132.391.268.583.408.138.1.274.195.41.292.184.137.37.275.545.415.136.108.277.218.416.33.171.14.347.282.515.428.135.12.276.242.41.363.163.143.327.288.486.44.135.13.274.258.407.39.15.147.308.297.458.45.137.141.276.293.413.44.14.143.277.293.413.438.254.288.505.58.749.878.112.138.22.28.333.419.138.179.281.359.42.543.112.15.219.306.328.456.133.18.26.36.381.538.107.157.211.318.313.477.123.184.24.374.356.557a23.39 23.39 0 0 1 .625 1.076l.264.479c.11.212.215.424.321.635.073.155.157.31.23.463.115.246.223.493.328.742.059.128.12.256.175.387.156.38.305.761.444 1.146l.085.251c.109.309.218.62.313.932.047.148.087.296.13.44.071.258.149.514.213.774.044.162.082.327.123.491.059.248.119.497.167.746.04.17.075.343.105.512.05.25.094.5.136.754l.081.514c.036.262.067.525.099.787.015.166.036.329.053.497.032.288.049.58.064.87a20.102 20.102 0 0 1 .061 1.74c0 1.293-.093 2.563-.27 3.806-1.13-2.185-3.072-3.768-4.754-4.806l-.005-.004a5.595 5.595 0 0 0-1.198-.707c-.137-.065-.27-.12-.41-.185-.297-.147-.557-.263-.782-.363-.018-.005-.028-.014-.047-.02a11.635 11.635 0 0 1-.376-.167 14.075 14.075 0 0 0-1.325-.58c-.013-.01-.02-.01-.03-.015-4.554-1.943-7.992-2.954-10.175-4.02l-.005-.002c-.152-.073-.301-.151-.445-.23-.037-.016-.067-.037-.106-.055-.102-.062-.21-.12-.308-.183a1.684 1.684 0 0 1-.135-.082c-.08-.053-.162-.104-.238-.16-.047-.032-.097-.07-.144-.105a2.237 2.237 0 0 1-.128-.099c-.039-.033-.077-.062-.114-.095-.055-.05-.112-.099-.165-.154Z" fill="url(#f)" />
	<defs>
		<radialGradient id="a" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(75.4789 0 0 76.2199 7.829 30.697)">
			<stop stop-color="#ED1D80" />
			<stop offset=".316" stop-color="#ED1C7D" />
			<stop offset=".835" stop-color="#ED1873" />
			<stop offset="1" stop-color="#ED176F" />
		</radialGradient>
		<radialGradient id="b" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-102.667 0 0 -103.674 63.692 23.87)">
			<stop stop-color="#EE1E37" />
			<stop offset="1" stop-color="#E51E26" />
		</radialGradient>
		<radialGradient id="c" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(61.9042 0 0 62.5119 28.647 -.918)">
			<stop stop-color="#ED185A" />
			<stop offset=".144" stop-color="#EB1956" />
			<stop offset=".382" stop-color="#E71C4D" />
			<stop offset=".683" stop-color="#E11E3B" />
			<stop offset="1" stop-color="#DB1F26" />
		</radialGradient>
		<radialGradient id="d" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(49.8506 0 0 50.3399 26.022 2.13)">
			<stop stop-color="#ED1862" />
			<stop offset=".238" stop-color="#ED1866" />
			<stop offset=".63" stop-color="#ED1770" />
			<stop offset="1" stop-color="#ED177B" />
		</radialGradient>
		<radialGradient id="e" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-47.3264 0 0 -47.7909 30.826 -1.777)">
			<stop stop-color="#EE3237" />
			<stop offset="1" stop-color="#E31E26" />
		</radialGradient>
		<radialGradient id="f" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-54.8985 0 0 -55.4373 50.294 13.18)">
			<stop stop-color="#EE3237" />
			<stop offset="1" stop-color="#ED1C2A" />
		</radialGradient>
	</defs>
</svg>
