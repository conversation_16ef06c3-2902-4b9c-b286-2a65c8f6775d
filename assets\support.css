.icon-block {
  width: 104px;
  height: 132px;
}

.support-block {
  position: relative;
}

.overlap-icon-right-top {
  width: 323px;
  height: 62px;
  position: absolute;
  right: -118px;
  top: 0;
  opacity: 0.4;
  background: #dbc9da;
}

.overlap-icon-left-bottom {
  width: 156px;
  height: 252.186px;
  transform: rotate(-90deg);
  position: absolute;
  left: 0;
  bottom: 0;
  top: 28px;
  border-radius: 0 0 189.169px 189.169px;
  opacity: 0.4;
  background: #c2d3d2;
}

.support-block svg {
  position: absolute;
}

.support-block .left-svg {
  left: 0;
  bottom: 0;
}

.support-block .right-svg {
  right: 0;
  bottom: 0;
}

@media (max-width: 768px) {
  .text-block {
    margin-top: 0;
  }
  .overlap-icon-left-bottom {
    width: 175px;
    height: 246px;
    top: 62px;
  }

  .overlap-icon-right-top {
    width: 182px;
    height: 56px;
    position: absolute;
    right: 0;
    top: 0;
    opacity: 0.4;
    background: #dbc9da;
  }
  .icon-block {
    right: -40px;
    width: 68px;
    height: 56px;
  }
  .icon-block-left-bottom {
    left: -48px !important;
  }
}
