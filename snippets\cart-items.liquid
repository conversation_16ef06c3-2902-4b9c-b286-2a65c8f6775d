{% liquid
  assign scan_product_string = 'scan-product'
  assign health_pass_string = 'health-pass'
  assign pricing_page_url = '/pages/pricing'
%}

<div class="grid grid-cols-1 gap-4 {%if cart_popup and sidebar_item %}responsive-cart-item-block content-between{% endif %}">
  <ul class="product-list list-none space-y-4 {% if cart_popup %}{{ list_wrapper_classes }}{% endif %}">
    {% if has_health_pass_in_cart and is_subscription_active or is_subscription_cancelled %}
      <div
        class="flex items-center p-4 mb-4 text-sm text-secondary rounded-lg bg-primary-light border border-primary gap-3"
        role="alert"
      >
        <div class="warning-icon-block">
          <svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M26.0004 13.7598C24.8423 13.7598 23.9975 14.457 23.2747 15.3652C22.5677 16.2536 21.8153 17.5531 20.8849 19.1602L20.8439 19.2312L15.7483 28.0326L15.7072 28.1036C14.7733 29.7167 14.0183 31.0207 13.5986 32.0793C13.1697 33.1613 12.9852 34.244 13.565 35.2497C14.1449 36.2554 15.1744 36.6382 16.3256 36.8091C17.452 36.9764 18.9589 36.9763 20.8228 36.9763H20.9048H31.096H31.178C33.0419 36.9763 34.5488 36.9764 35.6752 36.8091C36.8264 36.6382 37.8559 36.2554 38.4358 35.2497C39.0156 34.244 38.8311 33.1613 38.4022 32.0793C37.9825 31.0207 37.2275 29.7167 36.2936 28.1036L36.2525 28.0326L31.1569 19.2312L31.1159 19.1603C30.1854 17.5531 29.4331 16.2536 28.7261 15.3652C28.0033 14.457 27.1585 13.7598 26.0004 13.7598ZM26.9421 22.2367C26.9421 21.7171 26.5209 21.2959 26.0013 21.2959C25.4817 21.2959 25.0605 21.7171 25.0605 22.2367V27.2542C25.0605 27.7738 25.4817 28.195 26.0013 28.195C26.5209 28.195 26.9421 27.7738 26.9421 27.2542V22.2367ZM27.2557 31.0174C27.2557 31.7102 26.6941 32.2718 26.0013 32.2718C25.3085 32.2718 24.7469 31.7102 24.7469 31.0174C24.7469 30.3246 25.3085 29.763 26.0013 29.763C26.6941 29.763 27.2557 30.3246 27.2557 31.0174Z" fill="#E0BB5C"/>
            <circle cx="26" cy="26" r="26" fill="#E0BB5C" fill-opacity="0.2"/>
          </svg>
        </div>
        <div class="flex-1 mesasge-block">
          <p class="text-base md:text-lg font-bold">
            {{ 'general.customer.customer_membership_warning_messasge.title' | t }}
          </p>
          <ul class="mt-1.5 list-disc list-inside">
            <li>
              {{ 'general.customer.customer_membership_warning_messasge.description_html' | t }}
            </li>
            <li>{{ 'general.customer.customer_membership_warning_messasge.action_message' | t }}</li>
            <li>
              {{ 'general.customer.customer_membership_warning_messasge.link_message' | t }}
              <a
                href="/apps/subscriptions#/subscriptions/{{ customer.metafields.appstle_subscription.subscriptions.value[0].id }}/detail"
                class="text-primary-gradient inline-flex !font-bold justify-center items-center"
              >
                {{ 'general.customer.view_membership' | t }}
              </a>
            </li>
          </ul>
        </div>
      </div>
    {% endif %}

    {% for line_item in cart_items %}
      <li
        class="relative line-item border rounded-lg p-4 border-gray-2"
        data-line-item-key="{{- line_item.key -}}"
        data-product-variant-id="{{- line_item.product.selected_or_first_available_variant.id -}}"
        data-product-quantity="{{- line_item.quantity -}}"
      >
        <div class="line-item-inner-block flex gap-4">
          <div class="overflow-hidden rounded-md">
            {% if line_item.image %}
              <a
                {% if line_item.product.type == scan_product_string or line_item.product.type == health_pass_string %}
                  href="{{- pricing_page_url -}}"
                {% else %}
                  href="{{- line_item.url -}}"
                {% endif %}
              >
                <img
                  class="product-item-img w-[90px] min-h-[90px] h-full rounded-md"
                  src="{{ line_item.image.src | image_url }}"
                  alt="{{ line_item.image.alt | escape }}"
                  loading="lazy"
                  width="{{ line_item.image.width }}"
                  height="{{ line_item.image.height }}"
                >
              </a>
            {% endif %}
          </div>
          <div class="line-item-content-group flex flex-1 flex-col">
            <div class="flex justify-between">
              <div class="grid">
                <a
                  {% if line_item.product.type == scan_product_string or line_item.product.type == health_pass_string %}
                    href="{{- pricing_page_url -}}"
                  {% else %}
                    href="{{- line_item.url -}}"
                  {% endif %}
                  class="{%- if title_classes -%}{{- title_classes -}}{%- else %} product-title text-lg md:text-2xl font-bold text-secondary{%- endif -%}"
                >
                  {{- line_item.product.title -}}
                </a>
                {% unless line_item.product.has_only_default_variant %}
                  <small class="text-sm text-gray-5">
                    {{ line_item.variant.title }}
                  </small>
                {% endunless %}

                {% unless line_item.selling_plan_allocation == null %}
                  <small class="text-sm text-gray-5">
                    {{- 'general.customer.annual_membership' | t -}}
                  </small>
                  {% if line_item.product.metafields.subscription.free_products_with_subscription.value != null %}
                    {% for free_product in line_item.product.metafields.subscription.free_products_with_subscription.value %}
                      <small class="text-sm text-gray-5">
                        {{- free_product.title.value | prepend: ' ' | prepend: free_product.number_of_quantity.value -}}
                      </small>
                    {% endfor %}
                  {% endif %}
                {% endunless %}
              </div>
              <button
                type="button"
                data-remove-line-item
                data-line-item-key="{{ line_item.key }}"
                onclick="onRemoveCartItem(this)"
                aria-label="{{ 'cart.remove' | t }}"
                class="flex"
              >
                {{- 'icon-trash.svg' | inline_asset_content -}}
              </button>
            </div>
            <div class="flex flex-1 items-end justify-between text-sm">
              {% unless line_item.product.type contains 'pay-per-scan'
                or line_item.selling_plan_allocation.selling_plan
              %}
                <div class="quantity-input-button-group relative flex items-center max-w-[8rem]">
                  <button
                    type="button"
                    {% unless line_item.quantity > 1 %}
                      disabled
                    {% endunless %}
                    class="decrement-button size-7 inline-flex justify-center items-center text-sm font-medium rounded-full bg-[#E6E6E6]"
                    id="decrement-button_{{ line_item.key }}"
                  >
                    {{- 'icon-minus.svg' | inline_asset_content -}}
                  </button>
                  <input
                    type="text"
                    name="updates[]"
                    id="quantity-input_{{ line_item.key }}"
                    data-input-min="{{ line_item.product.selected_or_first_available_variant.quantity_rule.min }}"
                    data-input-max=""
                    class="quantity-input w-9 p-2 h-7 text-center focus:outline-none"
                    value="{{ line_item.quantity }}"
                    onchange="onChangeCartQty(this)"
                  >
                  <button
                    type="button"
                    class="increment-button size-7 inline-flex justify-center items-center text-sm font-medium rounded-full bg-[#E6E6E6]"
                    id="increment-button_{{ line_item.key }}"
                  >
                    {{- 'icon-plus.svg' | inline_asset_content -}}
                  </button>
                </div>
              {% endunless %}

              <p class="product-item-price flex justify-end w-full">
                <span class="text-sm font-bold">
                  {%- if line_item.original_price != line_item.final_price %}
                    <div class="text-sm flex gap-2">
                      <span class="hidden">
                        {{- 'product.price.regular_price' | t -}}
                      </span>
                      <s class="product-item-price-final text-sm font-bold text-gray-3">
                        {{- line_item.original_price | money -}}
                      </s>
                      <span class="hidden">
                        {{- 'product.price.sale_price' | t -}}
                      </span>
                      <strong class="product-item-price-final text-sm font-bold text-gray-8">
                        {{- line_item.final_price | money -}}
                      </strong>
                    </div>
                  {%- else -%}
                    <div class="product-item-price-final text-sm font-bold text-gray-8">
                      {{- line_item.original_line_price | money -}}
                    </div>
                  {%- endif -%}
                </span>
              </p>
            </div>
          </div>
        </div>
      </li>
    {% endfor %}
    {% if cart_popup %}
      {% render 'alert-message', cart_items: cart_items %}
    {% endif %}
  </ul>
  {% if cart_popup %}
    <div class="cart-popup-footer p-4 grid gap-4" style="box-shadow: 0px -11px 28px -9px rgba(33, 35, 38, 0.1);">
      {% if cart.item_count != 0 %}
        <div id="cart-popup-subtotal" class="flex justify-between items-center leading-none">
          <p class="paragraph-text">{{ 'cart.subtotal' | t }}</p>
          <p>
            {% if cart.items_subtotal_price > cart.total_price %}
              <s class="text-base font-bold text-gray-8">
                {{ cart.items_subtotal_price | money }}
              </s>
              <b>
                {{ cart.total_price | money }}
              </b>
            {% else %}
              <b class="text-base font-bold text-gray-8">
                {{ cart.total_price | money }}
              </b>
            {% endif %}
          </p>
        </div>
        {% render 'checkout-button',
          classes: 'button-primary-gradient text-center !text-base !w-full !py-3',
          is_subscription_active: is_subscription_active,
          is_subscription_cancelled: is_subscription_cancelled,
          is_subscription_expired: is_subscription_expired,
          is_subscription_failed: is_subscription_failed,
          has_health_pass_in_cart: has_health_pass_in_cart
        %}
      {% endif %}
    </div>
  {% endif %}
</div>
