{%- style -%}
   #general-marquee-{{ section.id }} {
    background-color: {{ section.settings.bg_color }};
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    #general-marquee-{{ section.id }} {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .general-marquee {
    display: block;
    overflow: hidden;
  }

  .general-marquee ul {
    padding: 0;
    margin: 0;
    gap: 12px;
    list-style: none;
    display: inline-flex;
    align-items: center;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
  }

  @media (prefers-reduced-motion: reduce) {
    .general-marquee ul {
      animation: none;
    }
  }

  .general-marquee ul:hover {
    animation-play-state: paused;
  }

  .general-marquee ul li {
    border: 1px solid rgba(0, 0, 0, 0.10);
    background: rgba(251, 233, 233, 0.10);
  }
{%- endstyle -%}

{%- javascript -%}
  class GeneralMarquee extends HTMLElement {
    constructor() {
      super();

      this.init();

      document.addEventListener('shopify:section:load', (e) => {
        if (e.target.querySelector('.general-marquee')) {
          init();
        }
      });
    }

    init() {
      const list = this.querySelector('ul');
      const marqueeWidth = list.scrollWidth;
      const marqueeLength = list.querySelectorAll('li').length;

      list.insertAdjacentHTML('beforeEnd', list.innerHTML);
      list.insertAdjacentHTML('beforeEnd', list.innerHTML);

      list.querySelectorAll('li').forEach((item, index) => {
        if (index >= marqueeLength) {
          item.setAttribute('aria-hidden', 'true');
        }
      });

      let style = `
      <style>
        #general-marquee-${this.dataset.sectionId} ul {
          animation-name: general-marquee-animation-${this.dataset.sectionId};
          animation-duration: ${this.dataset.animationDuration}s;
        }
        @keyframes general-marquee-animation-${this.dataset.sectionId} {
          to { transform: translateX(-${marqueeWidth}px); }
        }
      </style>
    `;
      if (this.dataset.marqueeDirection === 'right') {
        style += `
        <style>
          @keyframes general-marquee-animation-${this.dataset.sectionId} {
            from { transform: translateX(-${marqueeWidth}px); }    
            to { transform: 0); }    
          }
        </style>
      `;
      }

      this.insertAdjacentHTML('beforeBegin', style);
    }
  }
  customElements.define('general-marquee', GeneralMarquee);
{%- endjavascript -%}

<general-marquee
  id="general-marquee-{{- section.id -}}"
  class="general-marquee"
  data-section-id="{{- section.id -}}"
  data-animation-duration="{{- section.settings.animation_duration -}}"
  data-marquee-direction="{{- section.settings.marquee_direction -}}"
>
  <ul
    class="mb-0"
    aria-label="{{- section.settings.list_label -}}"
  >
    {% for block in section.blocks %}
      <li
        class="rounded-full text-gray-7 marquee-item shrink-0 py-3 md:py-5 px-4 md:px-7 text-base md:text-xl"
        {{ block.shopify_attributes }}
      >
        {{- block.settings.text -}}
      </li>
    {% endfor %}
  </ul>
</general-marquee>

{% schema %}
{
  "name": "General - Marquee",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "list_label",
      "label": "List label",
      "default": "Features",
      "info": "It is used for accessibility."
    },
    {
      "type": "header",
      "content": "Marquee"
    },
    {
      "type": "range",
      "id": "animation_duration",
      "label": "Animation duration",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 30,
      "unit": "sec"
    },
    {
      "type": "select",
      "id": "marquee_direction",
      "label": "Marquee direction",
      "default": "left",
      "options": [
        { "value": "left", "label": "Left" },
        { "value": "right", "label": "Right" }
      ]
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "Gap (between items)",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Padding top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 6
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Padding bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 6
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Announce something here"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "General - Marquee",
      "blocks": [
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        }
      ]
    }
  ]
}
{% endschema %}
