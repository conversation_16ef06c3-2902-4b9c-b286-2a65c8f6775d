{{ 'pricing.css' | asset_url | stylesheet_tag }}
{%- style -%}
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
      margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-margin {
        margin-top: {{ section.settings.margin_top }}px;
        margin-bottom: {{ section.settings.margin_bottom }}px;
      }
    }

  .badge-block {
      background: rgba(255, 255, 255, 0.20);
  }

  .save-badge {
    border-radius: 13px;
    background: #D2FFC3;
    padding: 4px 8px;
    font-size: 12px;
    line-height: 12px;
    color:  #235611;
  }
{%- endstyle -%}

{% liquid
  assign standard_card_type_string = 'standard-with-buy-now-button'
  assign buy_now_string = 'product.purchase' | t
%}

<section
  class="py-12 md:py-16 lg:py-20 section-{{ section.id }}-margin"
  {% if section.settings.is_enabled_bg_color %}
    style="background: linear-gradient(180deg, #EEF1F1 0%, rgba(238, 241, 241, 0.00) 100%);"
  {% endif %}
>
  <div class="container mx-auto px-4 lg:px-0 relative">
    <div class="text-block max-w-2xl mx-auto text-center mb-6 md:mb-10 w-full md:w-[50%]">
      {% unless section.settings.title == blank %}
        <h2 class="section-heading-40 text-secondary">
          {{- section.settings.title -}}
        </h2>
      {% endunless %}
      {% unless section.settings.description == blank %}
        <div class="paragraph-text mt-4">{{- section.settings.description -}}</div>
      {% endunless %}
    </div>
    <div class="products-lists w-full flex flex-col md:flex-row gap-4 md:gap-6">
      {% for product in section.settings.products %}
        {% case forloop.index %}
          {% when 1 %}
            <div class="products-list standard-3d-body-scan">
              <p class="product-title text-xl text-secondary font-bold mb-4">{{ product.title }}</p>
              {% render 'price-block', product: product, forloop: forloop %}
              <div class="product-description mt-4 text-xs text-secondary">{{ product.description }}</div>
              <div class="horizontal-line"></div>
              <p class="px-2 text-xs text-secondary mb-3">1 Standard Body Scan</p>
              <ul class="space-y-2 text-secondary mb-2 border border-gray-2 py-4 px-2 rounded-xl relative">
                <p class="text-xs text-secondary card-overlap-text">Included in each scan</p>
                {% for item in product.metafields.custom.features.value %}
                  <li class="flex gap-2 items-start">
                    <div class="flex-none checkmark-icon size-5">
                      <img src="{{ 'checkmark-icon.svg' | asset_url }}" width="24" height="24" alt="{{ item.title }}">
                    </div>
                    <p class="text-sm text-gray-8">{{ item.title }}</p>
                  </li>
                {% endfor %}
              </ul>
              {% if product.price == 0 %}
                <p class="scans-available-message px-2 text-xs text-gray-7">
                  {{ 'sections.healthpass_offerings.scans_available_info' | t }}
                </p>
              {% endif %}
            </div>
          {% when 2 %}
            <div class="most-popular-slide p-1.5 bg-gradient-primary rounded-2xl card-type-{{ section.settings.card_type }} col-span-2">
              <div class="most-polpular-block px-2 flex justify-between items-center mb-1.5">
                <p class="text-sm font-bold text-white">{{ 'sections.healthpass_offerings.most_popular' | t }}</p>
                <div class="badge-block px-1.5 py-1 flex justify-between items-center gap-1 rounded-full md:bg-gray-2 relative min-w-max flex-none md:flex md:w-fit">
                  <div class="flex items-center rounded-full justify-center bg-white size-3.5">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                      <path d="M2.66602 7.15881L5.74755 10.2403L11.6305 4.35742" stroke="#CE274A" stroke-width="1.0085"/>
                    </svg>
                  </div>
                  <div class="HSA-FSA-badge-content flex justify-between">
                    <p class="text-xs text-white">{{ 'sections.health_product.badge' | t }}</p>
                  </div>
                </div>
              </div>
              <div class="grid md:grid-cols-2 gap-1.5">
                <div class="products-list card-type-{{ section.settings.card_type }}">
                  <p class="product-title text-xl text-secondary font-bold mb-4">{{ product.title }}</p>
                  {% render 'price-block', product: product, forloop: forloop %}
                  <div class="horizontal-line"></div>
                  <div class="product-description text-xs text-secondary">{{ product.description }}</div>
                  <div class="horizontal-line"></div>
                  <div class="flex gap-3 items-start">
                    <p class="px-2 text-xs text-secondary mb-3">1 Preventative Body Scan</p>
                  </div>
                  <ul class="space-y-2 text-secondary mb-2 border border-gray-2 py-4 px-2 rounded-xl relative">
                    <p class="text-xs text-secondary card-overlap-text">Included in each scan</p>
                    {% for item in product.metafields.custom.features.value %}
                      <li class="flex gap-2 items-start">
                        <div class="flex-none checkmark-icon size-5">
                          <img
                            src="{{ 'checkmark-icon.svg' | asset_url }}"
                            width="24"
                            height="24"
                            alt="{{ item.title }}"
                          >
                        </div>
                        <p class="text-sm text-gray-8">{{ item.title }}</p>
                      </li>
                    {% endfor %}
                  </ul>
                  <p class="text-xs text-gray-7 px-2 {% unless  section.settings.card_type != standard_card_type_string %}mb-4{% endunless %}">
                    {{- 'sections.healthpass_offerings.scans_available_info' | t -}}
                  </p>
                  {% if section.settings.card_type == standard_card_type_string %}
                    <div class="buy-now-button-block mt-5">
                      {% render 'product-form', product: product, button_text: buy_now_string %}
                    </div>
                  {% endif %}
                </div>
          {% when 3 %}
            {% liquid
              if product.metafields.subscription.free_products_with_subscription
                assign free_products = product.metafields.subscription.free_products_with_subscription.value
                for free_product in free_products
                  if free_product.vendor == 'Health Pass'
                    assign free_preventative_body_scans_product = free_product.product.value
                    assign free_preventative_body_scans_product_price = free_product.product.value.price | money_without_trailing_zeros
                    assign free_preventative_body_scans_product_unit = free_product.number_of_quantity
                    assign free_preventative_body_scans_product_title = free_product.title
                  else
                    assign free_blood_tests_product = free_product.product.value
                    assign free_blood_tests_product_price = free_product.product.value.price | money_without_trailing_zeros
                    assign free_blood_tests_product_unit = free_product.number_of_quantity
                  endif
                endfor
              endif
            %}

            <div class="products-list card-type-{{ section.settings.card_type }}">
              <p class="product-title text-xl text-secondary font-bold mb-4">{{ product.title }}</p>
              {% render 'price-block', product: product, forloop: forloop %}
              <div class="horizontal-line"></div>
              <div class="product-description text-xs text-secondary">{{ product.description }}</div>
              <div class="horizontal-line"></div>
              <div class="flex gap-3 justify-between items-center mb-3">
                <p class="px-2 text-xs text-secondary">
                  {{
                    free_preventative_body_scans_product_title
                    | prepend: ' '
                    | prepend: free_preventative_body_scans_product_unit
                  }}
                </p>
                {% assign percentage = 24 | append: '%' %}

                <span class="save-badge">
                  {{ 'product.save_24_per_scan' | t: percentage: percentage }}
                </span>
              </div>
              <ul class="space-y-2 text-secondary mb-2 border border-gray-2 py-4 px-2 rounded-xl relative">
                <p class="text-xs text-secondary card-overlap-text">Included in each scan</p>
                {% for item in product.metafields.custom.features.value %}
                  {% if item.show_membership_only != true %}
                    <li class="flex gap-2 items-start">
                      <div class="flex-none checkmark-icon size-5">
                        <img src="{{ 'checkmark-icon.svg' | asset_url }}" width="24" height="24" alt="{{ item.title }}">
                      </div>
                      <p class="text-sm text-gray-8">{{ item.title }}</p>
                    </li>
                  {% endif %}
                {% endfor %}
              </ul>
              <p class="text-xs text-gray-7 px-2 mb-4">
                {{- 'sections.healthpass_offerings.scans_available_info' | t -}}
              </p>
              <div class="px-2 mb-1 flex gap-2 items-center">
                <p class="text-xs text-gray-7">
                  {{
                    'product.free_blood_tests_extra_message'
                    | t: quantity: free_blood_tests_product_unit, price_per_test: free_blood_tests_product_price
                  }}
                </p>
                <span class="save-badge text-center w-12 flex-none">
                  {{ 'product.free' | t }}
                </span>
              </div>
              <ul class="space-y-2 text-secondary {% unless  section.settings.card_type != standard_card_type_string %}mb-4{% endunless %} border border-gray-2 py-4 px-2 rounded-xl">
                {% for item in product.metafields.custom.features.value %}
                  {% if item.show_membership_only == true %}
                    <li class="flex gap-2 items-start">
                      <div class="flex-none checkmark-icon size-5">
                        <img src="{{ 'checkmark-icon.svg' | asset_url }}" width="24" height="24" alt="{{ item.title }}">
                      </div>
                      <p class="text-sm text-gray-8">{{ item.title }}</p>
                    </li>
                  {% endif %}
                {% endfor %}
              </ul>
              {% if section.settings.card_type == standard_card_type_string %}
                <div class="buy-now-button-block mt-5">
                  {% render 'product-form', product: product, button_text: buy_now_string %}
                </div>
              {% endif %}
            </div>
            </div>
            </div>
        {% endcase %}
      {% endfor %}
    </div>

    {% unless section.settings.btn_text == blank %}
      <div class="health-pass-offering-button-block mt-6 md:mt-12 flex justify-center">
        {% render 'button-block', settings_context: section.settings %}
      </div>
    {% endunless %}
  </div>
</section>
{% schema %}
{
  "name": "Featured products",
  "tag": "section",
  "settings": [
    {
      "type": "header",
      "content": "General"
    },
    {
      "type": "checkbox",
      "id": "is_enabled_bg_color",
      "label": "Enable background color",
      "default": false
    },
    {
      "type": "select",
      "id": "card_type",
      "label": "Select card type",
      "default": "general",
      "options": [
        { "value": "general", "label": "General" },
        { "value": "standard-with-buy-now-button", "label": "Standard with buy now button" }
      ]
    },
    {
      "type": "header",
      "content": "Text"
    },
    {
      "type": "html",
      "id": "title",
      "label": "Title",
      "default": "Healthpass Offerings"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description (optional)"
    },
    {
      "type": "product_list",
      "id": "products",
      "label": "Products"
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Book Scan",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Btton URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        { "value": "_blank", "label": "Open in a New Tab" },
        { "value": "_self", "label": "Open in Same Tab" }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    },

    {
      "type": "header",
      "content": "Specing"
    },

    {
      "type": "range",
      "id": "margin_top",
      "label": "Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 64
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 32
    }
  ],
  "presets": [
    {
      "name": "Featured products"
    }
  ]
}
{% endschema %}
