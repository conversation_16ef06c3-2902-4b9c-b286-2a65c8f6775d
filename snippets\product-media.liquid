{% comment %}
  Accepts:
  - product_media: [Array]
  - size: {Number} (Optional) The preferred image size which the image width/height will be handled. (default is 600)
  - crop: {string} (top,center,bottom,left,right,region) //// https://shopify.dev/docs/api/liquid/filters/image_url#image_url-crop
  - class: {String} (Optional) An optional class to apply to the Img element

  Usage:
  {% render 'image-url' product_media: product.media, width: 600, height: 80 crop:"center" class: "product-thumbnail" %}
{% endcomment %}

{% for media in product_media %}
  {% liquid
    assign width = width | default: 600
    assign height = height
    assign loading = loading | default: 'lazy'
    assign crop = crop | default: 'center'

    assign media_loading = 'eager'
    if forloop.index > 2
      assign media_loading = 'lazy'
    endif
  %}
  {% if media.media_type == 'image' %}
    <img
      src="{{ media | image_url: width: width , height: height , crop: crop }}"
      class="{{- class -}}"
      alt="{{ product.images.alt }}"
      width="{{ product.images.width }}"
      height="{{ product.images.height }}"
      loading="{{ media_loading }}"
    >
  {% else %}
    <div class="p-2 bg-gray-2 flex justify-center items-center max-h-[428px] min-h-[428px]">
      <p class="text-base text-secondary p-2">The media type is not supported.</p>
    </div>
  {% endif %}
{% endfor %}
