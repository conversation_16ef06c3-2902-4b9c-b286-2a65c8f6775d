<div class="flex flex-col md:h-[calc(100vh-92px)] w-full pb-16 md:pb-0">
  <div class="video-block mx-auto w-full h-full order-2 md:order-none hidden md:block">
    <video
      class="md:absolute inset-0 w-full md:h-full object-cover "
      autoplay
      muted
      loop
      playsinline
      poster="https://cdn.shopify.com/s/files/1/0681/4364/4907/files/poster-image.png?v=1725951083"
      src="{% if section.settings.banner_video_url != blank %}{{ section.settings.banner_video_url }}{% else %}https://cdn.shopify.com/videos/c/o/v/b9b562ad1acf497eb0d74691097bfdd0.mp4{% endif %}"
      type="video/mp4"
    ></video>
  </div>
  <div
    class="md:absolute inset-0 flex flex-col md:flex-row items-center order-1 md:order-none"
    style="background: linear-gradient(0deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.20) 100%);"
  >
    <div
      class="md:absolute inset-0 flex items-center md:w-[74%]"
      style="background: linear-gradient(90deg, #FFF 16.67%, rgba(255, 255, 255, 0.00) 100%);"
    ></div>
    <div class="text-white container px-4 lg:px-0 md:z-[1]">
      <div class="content-block w-full lg:w-[528px] py-6">
        <div class="heading-block mb-4 ">
          <h1 class="text-[34px] leading-[38px] md:text-4xl lg:text-[64px] lg:leading-[70px] font-bold text-secondary inline-flex">
            {{ section.settings.heading }}
          </h1>
          {% if section.settings.gradient_heading %}
            <h1 class="text-[34px] leading-[38px] md:text-4xl lg:text-[64px] lg:leading-[70px] font-bold text-primary-gradient inline-flex">
              {{- section.settings.gradient_heading -}}
            </h1>
          {% endif %}
        </div>
        <div class="mb-6 md:mb-4 paragraph-text">
          {{- section.settings.sub_text -}}
        </div>
        <div class="button-block-home-page mt-4 ">
          {% render 'button-block', settings_context: section.settings %}
        </div>
        <div class="stats-block flex flex-row justify-between mt-6 md:mt-12 md:max-w-[480px] md:min-w-[448px]">
          <div class="stats-content text-secondary">
            <p class="text-[28px] md:text-[40px] leading-none flex gap-2 justify-start md:justify-center items-center font-bold">
              {{- section.settings.active_locations -}}
              <span class="paragraph-text-responsive w-20 flex-none">
                {{- 'sections.banner_with_video.subtext.active_locations' | t -}}
              </span>
            </p>
          </div>
          <div class="stats-content text-secondary">
            <p class="text-[28px] md:text-[40px] leading-none flex gap-2 justify-start md:justify-center items-center font-bold">
              {{- section.settings.body_scans_performed -}}
              <span class="paragraph-text-responsive w-24 flex-none">
                {{- 'sections.banner_with_video.subtext.body_scans_performed' | t -}}
              </span>
            </p>
          </div>
        </div>
        {% if section.settings.HSA_FSA_badge %}
          <div class="HSA-FSA-badge-block mt-6 py-3 px-4 md:mt-8 flex justify-between gap-4 rounded-full md:bg-gray-2 relative w-full md:w-fit">
            <div class="HSA-FSA-badge-content flex justify-between">
              <p class="text-base font-bold text-white">{{ 'sections.health_product.badge' | t }}</p>
            </div>
            <div class="flex items-center rounded-full justify-center bg-white size-6">
              <svg xmlns="http://www.w3.org/2000/svg" width="13" height="14" viewBox="0 0 13 14" fill="none">
                <path d="M10.18 3.53555C10.391 3.38296 10.6873 3.39421 10.887 3.57656C11.1154 3.78588 11.1313 4.14108 10.9222 4.36953L5.4085 10.3842C5.30512 10.497 5.16007 10.5634 5.00713 10.5668C4.85424 10.57 4.70609 10.5099 4.59795 10.4018L2.09209 7.8959L2.01982 7.80801C1.87637 7.59028 1.90055 7.29454 2.09209 7.10293C2.28368 6.91135 2.57942 6.88722 2.79717 7.03067L2.88506 7.10293L4.97686 9.19375L10.095 3.61172L10.18 3.53555Z" fill="#161618"/>
              </svg>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
    <div class="video-block px-4 mx-auto w-full h-full order-2 md:order-none block md:hidden relative rounded-3xl">
      <video
        id="heroVideo"
        class="inset-0 w-full md:h-full object-cover rounded-3xl"
        muted
        loop
        playsinline
        poster="https://cdn.shopify.com/s/files/1/0681/4364/4907/files/poster-image.png?v=1725951083"
        src="{% if section.settings.banner_video_url != blank %}{{ section.settings.banner_video_url }}{% else %}https://cdn.shopify.com/videos/c/o/v/b9b562ad1acf497eb0d74691097bfdd0.mp4{% endif %}"
        type="video/mp4"
      ></video>

      <!-- Color Overlay with Linear Gradient -->
      <div
        class="absolute inset-0 pointer-events-none"
        style="background: linear-gradient(0deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.20) 100%);"
      ></div>

      <!-- Custom Play/Pause Button -->
      <button
        id="videoPlayPauseButton"
        class="absolute inset-0 flex items-center justify-center text-white text-4xl z-[1]"
        onclick="handleVideoToggle()"
      >
        <img
          src="{{ 'primary-play-icon.svg' | asset_url }}"
          width="68"
          height="68"
          id="videoPlayIcon"
          class="flex items-center justify-center size-16 transition duration-300 transform rounded-full shadow-2xl group-hover:scale-110"
        >
      </button>
    </div>
  </div>
</div>

<script>
  const heroVideo = document.getElementById('heroVideo');
  const videoPlayPauseButton = document.getElementById('videoPlayPauseButton');
  const videoPlayIcon = document.getElementById('videoPlayIcon');

  function handleVideoToggle() {
    if (heroVideo.paused) {
      heroVideo.play();
      videoPlayIcon.classList.add('hidden');
    } else {
      heroVideo.pause();
      videoPlayIcon.classList.remove('hidden');
    }
  }

  // Also allow tap on video to pause/play
  heroVideo.addEventListener('click', handleVideoToggle);
</script>

{% schema %}
{
  "name": "Home page",
  "class": "overflow-hidden w-full",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Get Your Whole"
    },
    {
      "type": "text",
      "id": "gradient_heading",
      "label": "Heading",
      "default": "Body Scan"
    },
    {
      "type": "richtext",
      "id": "sub_text",
      "label": "Heading",
      "default": "<p>Healthpass helps you better understand your health with a 35-second infrared light scan using artificial intelligence to measure hundreds of biomarkers and predict dozens of health outcomes.</p>"
    },
    {
      "type": "text",
      "id": "active_locations",
      "label": "Active locations",
      "default": "2,000+"
    },
    {
      "type": "text",
      "id": "body_scans_performed",
      "label": "Body scans performed",
      "default": "2.1M"
    },
    {
      "type": "checkbox",
      "id": "HSA_FSA_badge",
      "label": "HSA & FSA badge",
      "default": true
    },
    {
      "type": "url",
      "id": "banner_video_url",
      "label": "Paste video link to show video"
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Book Scan",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Button URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        { "value": "_blank", "label": "Open in a New Tab" },
        { "value": "_self", "label": "Open in Same Tab" }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    }
  ]
}
{% endschema %}
