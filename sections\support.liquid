{{ 'support.css' | asset_url | stylesheet_tag }}

<div class="section-{{ section.id }}-margin py-8 md:py-12">
  <div class="container px-4 lg:px-0 max-w-[800px]">
    <div class="flex relative flex-col mx-auto justify-start md:justify-center py-10 px-6 bg-gray-1 overflow-hidden rounded-2xl">
      <div class="icon-block absolute right-0 -top-6 md:top-0 z-[2]">
        <div class="absolute right-0 top-0 z-[2]">
          <svg xmlns="http://www.w3.org/2000/svg" width="104" height="132" viewBox="0 0 104 132" fill="none">
            <path opacity="0.4" d="M140.024 130.465C65.5035 140.716 10.3427 62.0027 1.08474e-05 -4.62891L140.024 -4.62889L140.024 130.465Z" fill="#EEA8BA"/>
          </svg>
        </div>
        <div class="overlap-icon-right-top"></div>
      </div>
      <div class="text-block w-full text-center mx-auto">
        <h1 class="section-heading-40 text-secondary text-center">
          {{ section.settings.title }}
        </h1>
        {% unless section.settings.paragraph == blank %}
          <div class="mt-2 text-base text-center">
            {{ section.settings.paragraph }}
          </div>
        {% endunless %}
      </div>
      <div class="icon-block-left-bottom absolute bottom-0 left-0 z-[1]">
        <svg xmlns="http://www.w3.org/2000/svg" width="111" height="129" viewBox="0 0 111 129" fill="none">
          <path opacity="0.2" d="M-22.3305 282.371C-22.8359 203.808 -23.5434 37.4198 -22.3305 0.371092C152.989 0.371094 157.4 282.371 -22.3305 282.371Z" fill="#F84642"/>
        </svg>
        <div class="overlap-icon-left-bottom"></div>
      </div>
    </div>
    <div class="chat-block support-block">
      <div id="chatSkeleton" class="w-full h-auto">
        <div class="flex gap-4 mt-8">
          <div class="w-8 h-8 shrink-0 bg-gray-3 rounded-full animate-pulse"></div>
          <div class="w-full space-y-2">
            <div class="w-full h-10 bg-gray-3 rounded-2xl animate-pulse"></div>
            <div class="w-full h-10 bg-gray-3 rounded-2xl animate-pulse"></div>
          </div>
        </div>
      </div>
      <typebot-standard class="w-full h-auto"></typebot-standard>
    </div>
  </div>
</div>

<script type="module">
  import Typebot from 'https://cdn.jsdelivr.net/npm/@typebot.io/js@0.3/dist/web.js';

  Typebot.initStandard({ typebot: 'b-2-c-customer-service-bot-5uqjvrq' });

  window.addEventListener('DOMContentLoaded', () => {
    const checkShadowRoot = setInterval(() => {
      const typebotEl = document.querySelector('typebot-standard');

      if (typebotEl && typebotEl.shadowRoot) {
        clearInterval(checkShadowRoot);

        const shadowRoot = typebotEl.shadowRoot;

        // Inject your custom styles inside the shadow DOM
        const style = document.createElement('style');
        style.textContent = `.typebot-bottom-spacer,.typebot-input-form button[type=submit] svg{display:none!important}.typebot-container{padding:0!important;position:relative!important;container-type:inline-size}.scrollable-container.typebot-chat-view{padding:28px 0!important;background-color:#fff}.typebot-host-bubble{color:#161618!important}.typebot-host-bubble>.bubble-typing{border-radius:16px;border:1px solid #ebebeb;background:#fff}.typebot-avatar-container .flex.justify-center.items-center.rounded-full.text-white.animate-fade-in.w-10.h-10.text-xl{width:28px!important;height:28px!important}.bubble1,.bubble2,.bubble3{background-color:#d22725!important;opacity:.8}.typebot-bottom-spacer{height:0!important}.typebot-input-form{width:100%!important;max-width:none!important;border-radius:16px!important;border:1px solid #ebebeb!important;background:#f4f4f4!important;padding:10px 16px 10px 10px!important}.typebot-input-form .text-input{background:0 0!important;padding:10px!important;font-size:16px!important;color:#161618!important}.typebot-input-form button[type=submit]{width:24px!important;height:24px!important;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 25' fill='none'%3E%3Cpath d='M6.5 11.3711L12 5.87109M12 5.87109L17.5 11.3711M12 5.87109L12 20.3711' stroke='white' stroke-width='1.2'/%3E%3C/svg%3E")!important;background-repeat:no-repeat!important;background-size:24px!important;background-position:center!important;display:inline-block!important;padding:22px;border-radius:16px}.typebot-input{all:unset;width:100%}.typebot-chat-chunk{gap:16px!important}.flex.flex-col.animate-fade-in{margin-bottom:8px!important}.max-w-chat-container{gap:16px!important}.typebot-guest-bubble{border-radius:16px!important}.typebot-guest-bubble{background:linear-gradient(270deg, #C8277D 0%, #D22725 100%)!important}`;
        shadowRoot.appendChild(style);

        const observer = new MutationObserver((mutations, obs) => {
          const typebotContainer = shadowRoot.querySelector('div.typebot-container');
          const chatView = shadowRoot.querySelector('.scrollable-container.typebot-chat-view');
          if (typebotContainer) {
            const chatSkeleton = document.getElementById('chatSkeleton');
            chatSkeleton.style.display = 'none';

            setTimeout(() => {
              chatSkeleton.remove();
            }, 500);
            obs.disconnect();
          }
        });

        observer.observe(shadowRoot, { childList: true, subtree: true });
      }
    }, 100);
  });
</script>

{% schema %}
{
  "name": "Support",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Need help?"
    },
    {
      "type": "richtext",
      "id": "paragraph",
      "label": "Add description",
      "default": "<p>Chat with our intelligent AI-powered chat bot for quick assistance!</p>"
    }
  ]
}
{% endschema %}
