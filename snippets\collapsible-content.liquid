<div class="accordion">
  {% for faq in shop.metaobjects.faq.values %}
    <div class="accordion-item border-b border-gray-2 last:border-none mb-6 md:mb-8">
      <button
        aria-expanded="false"
        class="accordion-title-block flex justify-between gap-7 w-full text-left"
      >
        <span class="grow text-base md:text-lg text-secondary font-medium text-left">{{ faq.question.value }}</span>
        <span class="flex-none w-10 icon" aria-hidden="true"></span>
      </button>
      <div class="accordion-body accordion-content w-[95%] mb-6 md:mb-8 mt-2">
        <p class="paragraph-text-responsive text-left">{{ faq.answer.value }}</p>
      </div>
    </div>
  {% endfor %}
</div>
