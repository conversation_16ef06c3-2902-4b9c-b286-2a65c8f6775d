<div class="filter-wrapper grid grid-cols-1 gap-2 lg:flex">
  <div class="search-by-near-me grow-0">
    <button
      type="button"
      id="nearMeButton"
      class="near-me-button flex justify-center items-center lg:inline-flex bg-white border w-full border-gray-2 text-gray-5 text-base rounded-md py-[10px] px-4 gap-2"
    >
      {{- 'icon-near-me.svg' | inline_asset_content -}}
      {{ 'general.near_me' | t }}
    </button>
  </div>
  <div class="filter-block grid grid-cols-1 lg:grid-cols-3 gap-2 grow">
    <div class="search-by-city grow">
      <div class="relative w-full">
        {% render 'search-input-with-cross-icon' %}
      </div>
    </div>
    <div class="business-type-list grow">
      {% render 'filter-dropdown',
        dropdown_id: 'businessTypeSelector',
        label_translation_key: 'general.filters_dropdown_text.facility_type',
        metaobject_namespace: 'business_type',
        value_key: 'business_name',
        title_key: 'business_name.value',
        item_class: 'business-type-item'
      %}
    </div>
    <div class="search-by-radious grow">
      {% render 'filter-dropdown',
        dropdown_id: 'radiusSelector',
        label_translation_key: 'general.filters_dropdown_text.search_radius_all',
        metaobject_namespace: 'location_search_radius',
        value_key: 'value',
        title_key: 'title',
        item_class: 'select-radius'
      %}
    </div>
  </div>
</div>
