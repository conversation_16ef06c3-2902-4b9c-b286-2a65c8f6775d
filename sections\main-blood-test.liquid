<div class="mt-7 md:mt-12">
  <div class="container px-4 lg:px-0">
    <div
      class="h-auto md:h-[450px] relative grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-10 items-start md:items-center rounded-3xl overflow-hidden"
      style="background:{{ section.settings.bg-color }}"
    >
      <div class="icon-block absolute left-0 -top-6 md:top-0 z-[2]">
        <div class="absolute left-6 top-0 z-[2]">
          <svg xmlns="http://www.w3.org/2000/svg" width="67" height="59" viewBox="0 0 67 59" fill="none">
            <path opacity="0.4" d="M65.7853 -14.9035C70.777 24.4277 32.4476 53.5412 0.00135803 59L0.00136126 -14.9035L65.7853 -14.9035Z" fill="#EEA8BA"/>
          </svg>
        </div>
        <div class="absolute left-[4.60rem] top-0 h-11 bg-[#DBC9DA] z-[1] w-[75px] opacity-40"></div>
      </div>
      <div class="text-block md:w-full pt-8 md:pt-0 px-4 md:px-0 md:pl-16">
        <div class="text-[28px] leading-8 md:text-[40px] md:leading-[46px] font-bold text-secondary mb-2">
          {{- section.settings.heading -}}
        </div>
        {% if section.settings.HSA_FSA_badge %}
          <div class="w-auto inline-block mb-2">
            <span
              class="p-1 flex gap-2 rounded-md"
              style="background: rgba(83, 204, 40, 0.30);"
            >
              {%- render 'green-check-mark-icon' %}
              <span class="text-xs text-secondary">{{ 'sections.health_product.badge' | t }}</span>
            </span>
          </div>
        {% endif %}
        <div class="paragraph-text w-full md:w-[78%]">
          {{- section.settings.sub_text -}}
        </div>
        <div class="btn-block mt-6">
          <a
            id="{{ section.settings.button_label |  split: ' ' }}"
            href="{{- section.settings.button_url -}}"
            class="inline-block text-center rounded-full button-primary-gradient-outline py-2 px-10 text-base"
          >
            <span class="text-primary-gradient text-sm font-bold">{{- section.settings.button_label -}}</span>
          </a>
        </div>
      </div>
      <div class="image-block size-full flex justify-center items-center pl-7 md:pl-0 pb-12 md:pb-0">
        {% if section.settings.image -%}
          <img
            src="{{ section.settings.image | image_url }}"
            alt="{{ section.settings.image.alt }}"
            width="{{ section.settings.image.width }}"
            height="{{ section.settings.image.height }}"
          >
        {% else %}
          {{- 'image' | placeholder_svg_tag: 'w-[430px] h-[270px] border border-gray-7 rounded-xl' -}}
        {% endif %}
      </div>
      <div class="center-icon-block absolute bottom-0 left-0 md:left-[40%] right-auto">
        <div class="z-[2] absolute bottom-[-12px] left-28">
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="63" viewBox="0 0 40 63" fill="none">
            <path opacity="0.4" d="M39.6978 0.122112C39.8454 23.066 40.052 71.6587 39.6978 82.4785C-11.5032 82.4785 -12.7913 0.122107 39.6978 0.122112Z" fill="#F84642"/>
          </svg>
        </div>
        <div
          class="absolute bottom-[-22px] bg-[#C2D3D2] h-14 z-[1] w-[206px] opacity-40 rounded-tr-full"
        ></div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Blood test",
  "class": "overflow-hidden w-full",
  "settings": [
    {
      "type": "richtext",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "sub_text",
      "label": "Heading",
      "default": "<p>Take control of your health with physician reviewed at-home blood tests.</p>"
    },
    {
      "type": "checkbox",
      "id": "HSA_FSA_badge",
      "label": "HSA & FSA badge",
      "default": true
    },
    {
      "type": "color",
      "id": "bg-color",
      "label": "Background color",
      "default": "#E4E7F4"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Label text for botton",
      "default": "Explore blood tests"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Paste video link to show video"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Add image to show on the right section"
    }
  ]
}
{% endschema %}
