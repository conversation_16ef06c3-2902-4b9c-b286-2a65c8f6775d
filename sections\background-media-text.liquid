{% style %}
  .bg-image {
    background: var(--color-gray-2);
    background-image: url('{{ section.settings.desktop_image | image_url }}');
    background-repeat: no-repeat, repeat;
    width: 100%;
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .main-wrapper-bg {
    height: clamp(750px, 770px, 780px);
  }

  .image-mask {
    position: absolute;
    top: 0;
    height: 270px;
    background: linear-gradient(180deg, var(--color-white) 0%, rgba(255, 255, 255, 0) 100%);
    width: 100%;
    z-index: 1;
  }
{% endstyle %}

<div class="relative main-wrapper-bg">
  <div class="image-mask"></div>
  <div class="relative flex items-center bg-image">
    <div class="container px-4 md:px-0">
      <div class="text-block w-full lg:w-[590px] py-6">
        <div class=" heading-level-2 text-secondary mb-6">
          {{- section.settings.heading -}}
        </div>
        <div class="text-base text-secondary">
          {{- section.settings.sub_text -}}
        </div>
        {% unless section.settings.btn_text == blank %}
          <div class="button-block mt-8 inline-flex">
            <a
              href="{{- section.settings.btn_url -}}"
              target="{{- section.settings.btn_target -}}"
              class="!text-base !font-bold inline-block text-center rounded-full cursor-pointer !py-3 !px-24 {% if section.settings.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
            >
              {% if section.settings.btn_variant != 'solid_primary' %}
                <span class="text-primary-gradient text-sm font-bold">
                  {{- section.settings.btn_text -}}
                </span>
              {% else %}
                {{- section.settings.btn_text -}}
              {% endif %}
            </a>
          </div>
        {% endunless %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Background media text",
  "class": "overflow-hidden w-full",
  "settings": [
    {
      "type": "richtext",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "sub_text",
      "label": "Heading",
      "default": "<p>Take control of your health with physician reviewed at-home blood tests.</p>"
    },
    {
      "type": "header",
      "content": "Button block"
    },
    {
      "type": "text",
      "id": "btn_text",
      "label": "Button text",
      "default": "Primary button",
      "info": "Leave empty to disable"
    },
    {
      "type": "url",
      "id": "btn_url",
      "label": "Btton URL"
    },
    {
      "type": "select",
      "id": "btn_target",
      "label": "Open file",
      "default": "_self",
      "options": [
        {
          "value": "_blank",
          "label": "Open in a New Tab"
        },
        {
          "value": "_self",
          "label": "Open in Same Tab"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_variant",
      "label": "Select button variant",
      "default": "solid_primary",
      "options": [
        {
          "value": "solid_primary",
          "label": "Primary solid button"
        },
        {
          "value": "outline_primary",
          "label": "Primary outline button"
        }
      ]
    },
    {
      "type": "image_picker",
      "id": "desktop_image",
      "label": "Upload an image for the desktop view",
      "info": "This image will appear in the right section of the desktop layout."
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Upload an image for the mobile view",
      "info": "This image will appear in the right section of the mobile layout."
    }
  ],
  "presets": [
    {
      "name": "Background media text"
    }
  ]
}
{% endschema %}
