document.addEventListener('DOMContentLoaded', function () {
  const passwordToggleButtons = document.querySelectorAll('.show-password');
  let isPasswordShown = false;

  // Function to toggle password visibility
  function toggleVisibility(button) {
    const inputBlock = button.closest('.input-block');
    const passwordInput = inputBlock.querySelector('.password-input');
    const eyeButton = button.querySelector('.eye-button');
    const eyeSlashButton = button.querySelector('.eye-slash-button');

    // Toggle password visibility based on current state
    if (isPasswordShown) {
      hidePassword(passwordInput);
      eyeButton.classList.remove('hidden');
      eyeSlashButton.classList.add('hidden');
    } else {
      showPassword(passwordInput);
      eyeButton.classList.add('hidden');
      eyeSlashButton.classList.remove('hidden');
    }
  }

  // Function to show password
  function showPassword(passwordInput) {
    passwordInput.setAttribute('type', 'text');
    isPasswordShown = true;
  }
  // Function to hide password
  function hidePassword(passwordInput) {
    passwordInput.setAttribute('type', 'password');
    isPasswordShown = false;
  }

  // Add click event listener to each password toggle button
  passwordToggleButtons.forEach((button) => {
    button.addEventListener('click', () => toggleVisibility(button));
  });
});
