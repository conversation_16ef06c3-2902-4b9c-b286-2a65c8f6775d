{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 1 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }

  .container-background {
    display: grid;
    min-height:685px;
    background: var(--color-gray-2);
    {% if section.settings.container_bg_color != blank %}
      background: {{ section.settings.container_bg_color }};
    {% elsif section.settings.image != blank %}
      background-image: url('{{ section.settings.image | image_url }}');
      background-size: cover;
      background-position: center;
    {% endif %}
  }
  .content-block{
    z-index: 2;
  }
  .content-block .heading-level-1, .mockup-heading {
    font-size: 52px;
    line-height:1.2;
  }
{%- endstyle -%}

<div class="section-{{ section.id }}-margin">
  <div class="container px-4 lg:px-0">
    <div class="container-background py-4 md:py-8 px-4 md:px-20 {{ section.settings.container_rounded -}}">
      <div class="content-block-wrapper justify-items-end">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'content_block' %}
              <div class="flex flex-col justify-start md:justify-center relative mb-7">
                {% unless block.settings.mockup_heading == blank %}
                  <p class="heading-level-1 mockup-heading !text-white">{{- block.settings.mockup_heading -}}</p>
                {% endunless %}
                <div class="content-block max-w-[580px]">
                  <h1 class="{{ block.settings.title_font_size }} text-secondary">{{- block.settings.heading -}}</h1>
                  <div class="max-w-[520px] mt-4 text-base {{ block.settings.pragraph_font_color }}">
                    {{- block.settings.paragraph -}}
                  </div>
                  {% if block.settings.btn_text != blank or block.settings.video_btn_text != blank %}
                    <div class="button-block mt-8 flex gap-4">
                      {% if block.settings.btn_text != blank %}
                        <div class="simple-button-block inline-flex">
                          <a
                            href="{{- block.settings.btn_url -}}"
                            target="{{- block.settings.btn_target -}}"
                            class="!text-base inline-block text-center rounded-full cursor-pointer !py-3 !px-12 {% if block.settings.btn_variant == 'solid_primary' %}{{ 'button-primary-gradient' }} {% else %} {{ 'button-primary-gradient-outline' }} {% endif %}"
                          >
                            {% if block.settings.btn_variant != 'solid_primary' %}
                              <span class="text-primary-gradient text-sm font-bold">
                                {{- block.settings.btn_text -}}
                              </span>
                            {% else %}
                              {{- block.settings.btn_text -}}
                            {% endif %}
                          </a>
                        </div>
                      {% endif %}
                      {% if block.settings.video_btn_text != blank -%}
                        {% render 'watch-now-button',
                          btn_text: block.settings.video_btn_text,
                          shopify_hosted_video_link: block.settings.shopify_hosted_video,
                          external_video_link: block.settings.external_video,
                          video_btn_variant: block.settings.video_btn_variant
                        %}
                      {% endif %}
                    </div>
                  {% endif %}
                </div>
              </div>
          {% endcase %}
        {% endfor -%}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Partner",
  "class": "overflow-hidden w-full",
  "settings": [
    {
      "type": "header",
      "content": "Set the vertical margin"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Adjust the top margin",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0,
      "info": "Adjust the space above the section"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Adjust the bottom margin",
      "min": 0,
      "max": 120,
      "step": 2,
      "default": 0,
      "info": "Adjust the space below the section"
    },
    {
      "type": "header",
      "content": "General settings for the container"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Add image to show on the right section"
    },
    {
      "type": "color_background",
      "id": "container_bg_color",
      "label": "Container background settings",
      "info": "Choose the background color for the container"
    },
    {
      "type": "select",
      "id": "container_rounded",
      "label": "Rounded corner",
      "info": "Choose the rounded corner size for the container",
      "default": "rounded-none",
      "options": [
        {
          "value": "rounded-none",
          "label": "None"
        },
        {
          "value": "rounded-sm",
          "label": "SM"
        },
        {
          "value": "rounded-md",
          "label": "MD"
        },
        {
          "value": "rounded-lg",
          "label": "LG"
        },
        {
          "value": "rounded-xl",
          "label": "XL"
        },
        {
          "value": "rounded-2xl",
          "label": "2XL"
        },
        {
          "value": "rounded-3xl",
          "label": "3XL"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "content_block",
      "name": "Content block",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Title settings"
        },
        {
          "type": "text",
          "id": "mockup_heading",
          "label": "Main Content Heading",
          "info": "Provide the main title for your content block.",
          "default": "Join the"
        },
        {
          "type": "html",
          "id": "heading",
          "label": "Heading text",
          "info": "Enter the main heading for your content block.",
          "default": "Know your Health Risks before its too late."
        },
        {
          "type": "select",
          "id": "title_font_size",
          "label": "Title font size",
          "info": "Choose the font size for the title (Heading).",
          "default": "heading-level-2",
          "options": [
            {
              "value": "heading-level-1",
              "label": "Level 1"
            },
            {
              "value": "heading-level-2",
              "label": "Level 2"
            },
            {
              "value": "heading-level-3",
              "label": "Level 3"
            },
            {
              "value": "heading-level-4",
              "label": "Level 4"
            }
          ]
        },
        {
          "type": "richtext",
          "id": "paragraph",
          "label": "Subtext",
          "info": "Enter additional text or description for your content block.",
          "default": "<p>Using advanced imaging technology and anthropometric science, Healthpass accurately estimates your health risks in seconds without exposing you to unnecessary radiation</p>"
        },
        {
          "type": "select",
          "id": "pragraph_font_color",
          "label": "Title font size",
          "default": "text-gray-9",
          "options": [
            {
              "value": "text-white",
              "label": "White"
            },
            {
              "value": "text-gray-9",
              "label": "Gray-9"
            },
            {
              "value": "text-secondary",
              "label": "Gray-7"
            },
            {
              "value": "text-soft-white",
              "label": "Soft White"
            },
            {
              "value": "text-cloud-gray",
              "label": "Cloud Gray"
            }
          ]
        },
        {
          "type": "header",
          "content": "Simple Button block"
        },
        {
          "type": "text",
          "id": "btn_text",
          "label": "Button text",
          "default": "Primary button",
          "info": "Leave empty to disable"
        },
        {
          "type": "url",
          "id": "btn_url",
          "label": "Button URL"
        },
        {
          "type": "select",
          "id": "btn_target",
          "label": "Open link",
          "default": "_self",
          "options": [
            {
              "value": "_blank",
              "label": "Open in a new tab"
            },
            {
              "value": "_self",
              "label": "Open in same tab"
            }
          ]
        },
        {
          "type": "select",
          "id": "btn_variant",
          "label": "Select button variant",
          "default": "solid_primary",
          "options": [
            {
              "value": "solid_primary",
              "label": "Primary solid button"
            },
            {
              "value": "outline_primary",
              "label": "Primary outline button"
            }
          ]
        },
        {
          "type": "header",
          "content": "Video button block",
          "info": "[Note:] Use only one of the options. When both External and Shopify-hosted are added, the Shopify-hosted option will be used."
        },
        {
          "type": "text",
          "id": "video_btn_text",
          "label": "Button text",
          "default": "Watch Now"
        },
        {
          "type": "video_url",
          "id": "external_video",
          "label": "External video",
          "accept": ["youtube", "vimeo"],
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
        },
        {
          "type": "video",
          "id": "shopify_hosted_video",
          "label": "Shopify-hosted video"
        },
        {
          "type": "select",
          "id": "video_btn_variant",
          "label": "Select button variant",
          "default": "solid_primary",
          "options": [
            {
              "value": "solid_primary",
              "label": "Primary solid button"
            },
            {
              "value": "outline_primary",
              "label": "Primary outline button"
            }
          ]
        }
      ]
    }
  ]
}
{% endschema %}
