{{ 'schedule-scan-modal.css' | asset_url | stylesheet_tag }}

<div class="schedule-scan-modal-overlay">
  <div
    class="schedule-scan-modal w-[90%] bg-white px-6 py-[30px] shadow-sm rounded-lg flex justify-center items-center lg:max-w-[540px] lg:w-[540px]"
  >
    <div class="modal-content w-full">
      <div id="scheduleScanSuccessMessage" class="succsess-message-block hidden">
        {% render 'request-success-message' %}
      </div>
      <div class="schedule-scan-form-block space-y-6">
        <div class="modal-header">
          <div class="flex justify-end">
            <button
              role="button"
              class="close-modal md:hidden schedule-scan-modal-close cursor-pointer hover:scale-125"
            >
              {{- 'icon-cross.svg' | inline_asset_content -}}
            </button>
          </div>
          <div class="flex items-center justify-center md:justify-between mb-1">
            <h3 class="text-[28px] leading-9 font-bold text-secondary">{{- 'general.schedule_scan.title' | t -}}</h3>
            <button
              role="button"
              class="close-modal hidden md:block schedule-scan-modal-close cursor-pointer hover:scale-125"
            >
              {{- 'icon-cross.svg' | inline_asset_content -}}
            </button>
          </div>
          <div class="flex justify-center">
            <p class="text-base text-center md:text-left text-gray-8 w-full">
              {{- 'general.schedule_scan.description' | t -}}
            </p>
          </div>
        </div>
        <div class="form-block">
          <form class="" id="scheduleScanForm">
            <input type="hidden" name="userId">
            <div class="grid gap-4 md:gap-6 grid-cols-1 mb-6">
              <div class="flex flex-col md:flex-row justify-stretch gap-4 md:gap-6">
                <div class="w-full">
                  <label for="firstName" class="block text-base font-bold mb-1 md:mb-1.5 text-secondary">
                    {{- 'input.first_name.label' | t -}}
                  </label>
                  <input
                    required
                    type="text"
                    name="firstName"
                    id="firstName"
                    placeholder="{{- 'input.first_name.placeholder' | t -}}"
                    class="input-field"
                  >
                </div>
                <div class="w-full">
                  <label for="lastName" class="block text-base font-bold mb-1 md:mb-1.5 text-secondary">
                    {{- 'input.last_name.label' | t -}}
                  </label>
                  <input
                    required
                    type="text"
                    name="lastName"
                    id="lastName"
                    placeholder="{{- 'input.last_name.placeholder' | t -}}"
                    class="input-field"
                  >
                </div>
              </div>
              <div class="relative">
                <label for="email" class="block text-base font-bold mb-1 md:mb-1.5 text-secondary">
                  {{- 'input.email.label' | t -}}
                </label>
                <input
                  required
                  type="text"
                  name="email"
                  id="email"
                  placeholder="{{- 'input.email.placeholder' | t -}}"
                  class="input-field"
                >
                <p id="requiredEmailErrorMessage" class="text-xs text-[#D22721] hidden absolute -bottom-4">
                  {{- 'input.email.error_message.required_email' | t -}}
                </p>
                <p id="invalidEmailErrorMessage" class="text-xs text-[#D22721] hidden absolute -bottom-4">
                  {{- 'input.email.error_message.invalid_email' | t -}}
                </p>
              </div>
              <div class="relative">
                <label for="phoneNumber" class="block text-base font-bold mb-1 md:mb-1.5 text-secondary">
                  {{- 'input.phone_number.label' | t -}}
                </label>
                <input required type="tel" name="phoneNumber" id="phoneNumber" class="input-field block w-full !pl-16">
                <div class="absolute -bottom-4">
                  <p id="phoneNumberErrorMessage" class="text-xs text-[#D22721] hidden">
                    {{- 'input.phone_number.error_message.invalid_phone' | t -}}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <input
                  id="checkbox"
                  type="checkbox"
                  value=""
                  class="w-4 h-4 bg-gray-100 border-gray-2 rounded-lg focus:ring-gray-2"
                >
                <label for="red-checkbox" class="ms-2 paragraph-text">
                  {{- 'general.schedule_scan.form.privacy_message' | t -}}
                  <a href="https://www.styku.com/privacy" target="_blank" class="underline-primary-text">
                    {{- 'general.schedule_scan.form.privacy_link' | t -}}.</a
                  ></label
                >
              </div>
            </div>
            <button
              id="submitButton"
              type="submit"
              class="button-primary-gradient text-base w-full flex justify-center items-center gap-4"
              disabled
            >
              {% render 'white-spinner-icon' %}
              <span>{{- 'general.schedule_scan.form.submit' | t -}}</span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
